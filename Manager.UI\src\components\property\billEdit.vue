<template>
  <el-dialog :title="dialog.title" v-model="dialog.show" width="800px" top="5vh" :close-on-click-modal="false" class="bill-edit-dialog">
    <el-form :model="billModel" :rules="rules" ref="formRef" label-width="120px" class="bill-edit-form">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="缴费对象" prop="paymentObjectId">
            <div style="display: flex; align-items: center;">
              <el-input v-model="paymentObjectDisplay" readonly placeholder="请选择缴费对象" style="flex: 1; margin-right: 8px;" />
              <el-button type="primary" @click="openObjectSelector">选择</el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="缴费明细" prop="paymentDetailId">
            <div style="display: flex; align-items: center;">
              <el-input v-model="paymentDetailDisplay" readonly placeholder="请先选择缴费对象" style="flex: 1; margin-right: 8px;" />
              <el-button type="primary" @click="openDetailSelector" :disabled="!billModel.paymentObjectId">选择</el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单价" prop="unitPrice">
            <el-input-number v-model="billModel.unitPrice" :min="0" :precision="2"
              style="width: 100%;" placeholder="从缴费明细自动获取" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单位" prop="unit">
            <el-input v-model="billModel.unit" maxlength="20" placeholder="从缴费明细自动获取" readonly />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数量" prop="quantity">
            <el-input-number v-model="billModel.quantity" :min="0" :precision="2"
              style="width: 100%;" placeholder="请输入数量" @change="calculateTotal" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上次数量" prop="lastQuantity">
            <el-input-number v-model="billModel.lastQuantity" :min="0" :precision="2"
              style="width: 100%;" placeholder="请输入上次数量" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="billModel.status" placeholder="请选择状态" style="width: 100%;">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属小区" prop="communityId">
            <el-select v-model="billModel.communityId" placeholder="请选择小区" style="width: 100%;" filterable>
              <el-option
                v-for="item in communityList"
                :key="item.id"
                :label="item.communityName"
                :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账单日期" prop="billDate">
            <el-date-picker v-model="billModel.billDate" type="date" placeholder="请选择账单日期"
              style="width: 100%;" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="支付金额" prop="payAmount">
            <el-input-number v-model="billModel.payAmount" :min="0" :precision="2"
              style="width: 100%;" placeholder="请输入支付金额"
              @change="onPayAmountChange" @input="onPayAmountChange" />
            <div class="form-tip">可手动修改支付金额</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="优惠金额" prop="discountAmount">
            <el-input-number v-model="billModel.discountAmount" :min="0" :precision="2"
              style="width: 100%;" placeholder="自动计算" readonly />
            <div class="form-tip">根据总金额和支付金额自动计算</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="总金额" prop="totalAmount">
            <el-input-number v-model="billModel.totalAmount" :min="0" :precision="2"
              style="width: 100%;" placeholder="数量×单价" readonly />
            <div class="form-tip">数量 × 单价的原始金额</div>
          </el-form-item>
        </el-col>

        <!-- 金额计算预览 -->
        <el-col :span="24" v-if="billModel.unitPrice && billModel.quantity">
          <el-form-item label="计算明细">
            <div class="amount-preview">
              <div style="margin-bottom: 8px;">
                <span>总金额：¥{{ billModel.unitPrice }} × {{ billModel.quantity }} = </span>
                <span style="font-weight: bold;">¥{{ billModel.totalAmount }}</span>
              </div>
              <div style="margin-bottom: 8px;" v-if="!isManualPayAmountChange && billModel.discount < 10">
                <span>折扣：{{ billModel.discount }}折 ({{ (billModel.discount * 10) }}%)</span>
              </div>
              <div style="margin-bottom: 8px;">
                <span>优惠金额：</span>
                <span style="color: #67c23a; font-weight: bold;">¥{{ billModel.discountAmount || 0 }}</span>
                <span v-if="isManualPayAmountChange" style="color: #909399; font-size: 12px;">（手动调整）</span>
                <span v-else-if="billModel.discount < 10" style="color: #909399; font-size: 12px;">（{{ billModel.discount }}折优惠）</span>
              </div>
              <div>
                <span>应付金额：</span>
                <span style="color: #f56c6c; font-weight: bold; font-size: 16px;">¥{{ billModel.payAmount }}</span>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit">保存</el-button>
      </div>
    </template>

    <!-- 缴费对象选择弹窗 -->
    <el-dialog title="选择缴费对象" v-model="objectSelectorDialog" width="800px" append-to-body>
      <div class="object-search">
        <el-input v-model="objectSearchKeyword" placeholder="搜索对象名称或手机号" clearable style="width: 300px; margin-bottom: 16px;" @input="searchObjects" />
      </div>
      <el-table :data="objectList" @row-click="selectObject" style="cursor: pointer;" max-height="400">
        <el-table-column prop="moniker" label="对象名称" width="150" align="center"/>
        <el-table-column prop="phone" label="手机号" width="130" align="center"/>
        <el-table-column prop="email" label="邮箱" align="center" show-overflow-tooltip/>
        <el-table-column prop="address" label="地址" align="center" show-overflow-tooltip/>
      </el-table>
      <template #footer>
        <el-button @click="objectSelectorDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 缴费明细选择弹窗 -->
    <el-dialog title="选择缴费明细" v-model="detailSelectorDialog" width="1000px" append-to-body>
      <div class="detail-search">
        <el-input v-model="detailSearchKeyword" placeholder="搜索缴费项目名称" clearable style="width: 300px; margin-bottom: 16px;" @input="searchDetails" />
      </div>
      <el-table :data="detailList" @row-click="selectDetail" style="cursor: pointer;" max-height="400">
        <el-table-column prop="id" label="明细ID" width="80" align="center"/>
        <el-table-column label="缴费项目" width="200" align="center">
          <template #default="scope">
            <span>{{ getItemSnapshotDisplay(scope.row.paymentItemSnapshot) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unitPrice" label="单价" width="100" align="center">
          <template #default="scope">
            <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.unitPrice }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" align="center"/>
        <el-table-column prop="quantity" label="数量" width="80" align="center"/>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="billingCycle" label="计费周期" width="100" align="center">
          <template #default="scope">
            <span>{{ getBillingCycleLabel(scope.row.billingCycle) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="effectiveDate" label="生效日期" align="center"/>
      </el-table>
      <template #footer>
        <el-button @click="detailSelectorDialog = false">取消</el-button>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { addPropertyPaymentBill, editPropertyPaymentBill, listPropertyPaymentObject, listPropertyPaymentDetail } from '@/api/property/paymentItems'
import { listCommunity } from '@/api/community/community'
import { listDictByNameEn } from '@/api/system/dict'
import mitt from '@/utils/mitt'
export default {
  name: 'billEdit',
  data() {
    return {
      billModel: {
        id: undefined,
        paymentObjectId: null,
        unitPrice: null,
        unit: '',
        quantity: null,
        status: '',
        communityId: null,
        billDate: '',
        paymentDetailId: null,
        lastQuantity: null,
        payAmount: null,
        discount: 10,
        discountAmount: null,
        totalAmount: null
      },
      communityList: [],
      statusOptions: [],
      paymentObjectDisplay: '',
      paymentDetailDisplay: '',
      objectSelectorDialog: false,
      objectSearchKeyword: '',
      objectList: [],
      objectSearchModel: {
        pageNum: 1,
        pageSize: 100
      },
      detailSelectorDialog: false,
      detailSearchKeyword: '',
      detailList: [],
      detailSearchModel: {
        pageNum: 1,
        pageSize: 100
      },
      isManualPayAmountChange: false, // 标记是否为手动修改支付金额
      dialog: {
        show: false,
        title: ''
      },
      rules: {
        paymentObjectId: [
          { required: true, message: '请选择缴费对象', trigger: 'change' }
        ],
        paymentDetailId: [
          { required: true, message: '请选择缴费明细', trigger: 'change' }
        ],
        quantity: [
          { required: true, message: '请输入数量', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        communityId: [
          { required: true, message: '请选择所属小区', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        // 加载账单状态字典
        const statusRes = await listDictByNameEn('property_payment_bill_status')
        this.statusOptions = (statusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))

        // 如果字典为空，使用默认选项
        if (this.statusOptions.length === 0) {
          this.statusOptions = [
            { value: 'pending', label: '待支付' },
            { value: 'paid', label: '已支付' },
            { value: 'cancelled', label: '已取消' },
            { value: 'refunded', label: '已退款' }
          ]
        }
      } catch (err) {
        console.error('加载字典数据失败:', err)
        // 使用默认状态选项
        this.statusOptions = [
          { value: 'pending', label: '待支付' },
          { value: 'paid', label: '已支付' },
          { value: 'cancelled', label: '已取消' },
          { value: 'refunded', label: '已退款' }
        ]
      }
    },

    /**
     * 加载小区列表
     */
    loadCommunityList() {
      listCommunity({ pageNum: 1, pageSize: 500 }).then(res => {
        this.communityList = res.data.data.list || []
      }).catch(err => {
        console.error('加载小区列表失败:', err)
      })
    },

    /**
     * 计算总金额（选择缴费明细后的自动计算）
     * 逻辑1：按折扣自动计算
     */
    calculateTotal() {
      const unitPrice = this.billModel.unitPrice || 0
      const quantity = this.billModel.quantity || 0
      const discount = this.billModel.discount || 10

      // 1. 总金额 = 数量 × 单价 (2 × 50 = 100)
      this.billModel.totalAmount = parseFloat((unitPrice * quantity).toFixed(2))

      // 如果不是手动修改支付金额，则按折扣逻辑计算
      if (!this.isManualPayAmountChange) {
        // 设置自动计算标志
        this._isAutoCalculating = true

        // 2. 优惠金额 = 总金额 - 总金额 × 折扣% (100 - 100 × 70% = 30)
        const discountPercent = discount / 10  // 将折扣转换为百分比（如7折 = 0.7）
        this.billModel.discountAmount = parseFloat((this.billModel.totalAmount - this.billModel.totalAmount * discountPercent).toFixed(2))

        // 3. 支付金额 = 总金额 - 优惠金额 (100 - 30 = 70)
        this.billModel.payAmount = parseFloat((this.billModel.totalAmount - this.billModel.discountAmount).toFixed(2))

        // 清除自动计算标志
        this.$nextTick(() => {
          this._isAutoCalculating = false
        })
      } else {
        // 如果是手动修改过支付金额，则重新计算优惠金额
        this.calculateDiscountFromPayAmount()
      }
    },

    /**
     * 支付金额变化时的处理
     * 逻辑2：手动修改支付金额后重新计算优惠金额
     */
    onPayAmountChange() {
      // 确保有总金额才进行计算
      if (this.billModel.totalAmount > 0) {
        this.isManualPayAmountChange = true
        this.calculateDiscountFromPayAmount()
      }
    },

    /**
     * 根据支付金额计算优惠金额
     */
    calculateDiscountFromPayAmount() {
      if (this.billModel.totalAmount && this.billModel.payAmount !== null) {
        // 优惠金额 = 总金额 - 手动修改后的支付金额
        this.billModel.discountAmount = parseFloat((this.billModel.totalAmount - this.billModel.payAmount).toFixed(2))

        // 确保优惠金额不为负数
        if (this.billModel.discountAmount < 0) {
          this.billModel.discountAmount = 0
          this.billModel.payAmount = this.billModel.totalAmount
        }
      }
    },

    /**
     * 打开缴费对象选择器
     */
    openObjectSelector() {
      this.objectSelectorDialog = true
      this.searchObjects()
    },

    /**
     * 搜索缴费对象
     */
    searchObjects() {
      const params = {
        ...this.objectSearchModel
      }

      // 如果有搜索关键词，同时搜索对象名称和手机号
      if (this.objectSearchKeyword) {
        params.moniker = this.objectSearchKeyword
        params.phone = this.objectSearchKeyword
      }

      listPropertyPaymentObject(params).then(res => {
        this.objectList = res.data.data.list || []
      }).catch(err => {
        console.error('搜索缴费对象失败:', err)
      })
    },

    /**
     * 选择缴费对象
     */
    selectObject(object) {
      this.billModel.paymentObjectId = object.id
      this.paymentObjectDisplay = object.moniker + (object.phone ? ` (${object.phone})` : '')
      this.objectSelectorDialog = false

      // 清空缴费明细选择
      this.billModel.paymentDetailId = null
      this.paymentDetailDisplay = ''
      this.billModel.unitPrice = null
      this.billModel.unit = ''
      this.billModel.discount = 10
    },

    /**
     * 打开缴费明细选择器
     */
    openDetailSelector() {
      if (!this.billModel.paymentObjectId) {
        this.$message.warning('请先选择缴费对象')
        return
      }
      this.detailSelectorDialog = true
      this.searchDetails()
    },

    /**
     * 搜索缴费明细
     */
    searchDetails() {
      const params = {
        ...this.detailSearchModel,
        paymentObjectId: this.billModel.paymentObjectId
      }

      listPropertyPaymentDetail(params).then(res => {
        this.detailList = res.data.data.list || []
      }).catch(err => {
        console.error('搜索缴费明细失败:', err)
      })
    },

    /**
     * 选择缴费明细
     */
    selectDetail(detail) {
      this.billModel.paymentDetailId = detail.id
      this.billModel.unitPrice = detail.unitPrice
      this.billModel.unit = detail.unit
      this.billModel.lastQuantity = detail.quantity
      this.billModel.discount = detail.discount || 10

      // 重置手动修改标志，使用折扣逻辑计算
      this.isManualPayAmountChange = false

      // 显示缴费明细信息
      const itemName = this.getItemSnapshotDisplay(detail.paymentItemSnapshot)
      this.paymentDetailDisplay = `${itemName} (¥${detail.unitPrice}/${detail.unit})`

      this.detailSelectorDialog = false
      this.calculateTotal()
    },

    /**
     * 获取项目快照显示
     */
    getItemSnapshotDisplay(snapshot) {
      if (!snapshot) return '--'

      try {
        const item = JSON.parse(snapshot)
        return item.paymentItemName || snapshot
      } catch (error) {
        return snapshot
      }
    },

    /**
     * 获取状态类型
     */
    getStatusType(status) {
      const statusMap = {
        'active': 'success',
        'inactive': 'danger',
        'pending': 'warning',
        'expired': 'info'
      }
      return statusMap[status] || 'info'
    },

    /**
     * 获取状态标签
     */
    getStatusLabel(status) {
      const option = this.statusOptions.find(item => item.value === status)
      return option ? option.label : status || '--'
    },

    /**
     * 获取计费周期标签
     */
    getBillingCycleLabel(cycle) {
      const cycleMap = {
        'monthly': '月度',
        'quarterly': '季度',
        'yearly': '年度'
      }
      return cycleMap[cycle] || cycle || '--'
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.billModel = {
        id: undefined,
        paymentObjectId: null,
        unitPrice: null,
        unit: '',
        quantity: null,
        status: '',
        communityId: null,
        billDate: '',
        paymentDetailId: null,
        lastQuantity: null,
        payAmount: null,
        discount: 10,
        discountAmount: null,
        totalAmount: null
      }
      this.paymentObjectDisplay = ''
      this.paymentDetailDisplay = ''
      this.isManualPayAmountChange = false // 重置手动修改标志
      this.$refs.formRef?.resetFields()
    },

    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        const api = this.billModel.id ? editPropertyPaymentBill : addPropertyPaymentBill
        api(this.billModel).then(() => {
          this.$emit('search')
          this.dialog.show = false
          this.$message.success('操作成功')
        }).catch(err => {
          this.$message.error(err.data?.errorMessage || '操作失败')
        })
      })
    }
  },
  watch: {
    // 监听支付金额变化
    'billModel.payAmount'(newVal, oldVal) {
      // 只有在有总金额且不是程序自动设置时才触发手动修改逻辑
      if (this.billModel.totalAmount > 0 && newVal !== oldVal && newVal !== null) {
        // 使用nextTick确保在下一个事件循环中执行，避免与其他计算冲突
        this.$nextTick(() => {
          if (!this._isAutoCalculating) {
            this.isManualPayAmountChange = true
            this.calculateDiscountFromPayAmount()
          }
        })
      }
    }
  },
  async mounted() {
    await this.initDictData()
    this.loadCommunityList()

    mitt.on('openBillEdit', (data) => {
      this.billModel = { ...data }

      // 如果有缴费对象ID，需要获取对象信息显示
      if (data.paymentObjectId) {
        // 这里可以根据ID获取对象信息，暂时使用ID显示
        this.paymentObjectDisplay = `对象ID: ${data.paymentObjectId}`
      }

      // 如果有缴费明细ID，需要获取明细信息显示
      if (data.paymentDetailId) {
        this.paymentDetailDisplay = `明细ID: ${data.paymentDetailId}`
      }

      this.dialog.show = true
      this.dialog.title = '编辑物业账单'
    })

    mitt.on('openBillAdd', () => {
      this.resetForm()
      this.dialog.show = true
      this.dialog.title = '新增物业账单'
    })
  },
  beforeUnmount() {
    mitt.off('openBillEdit')
    mitt.off('openBillAdd')
  }
}
</script>

<style scoped>
.bill-edit-dialog :deep(.el-dialog__body) {
  padding-top: 10px;
  padding-bottom: 0;
}

.bill-edit-form {
  padding: 0 10px;
}

.dialog-footer {
  padding: 10px 24px 18px 0;
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.object-search {
  margin-bottom: 16px;
}

.detail-search {
  margin-bottom: 16px;
}

.amount-preview {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>