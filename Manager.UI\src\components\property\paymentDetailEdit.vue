<template>
  <el-dialog :title="dialog.title" v-model="dialog.show" width="800px" top="5vh" :close-on-click-modal="false" class="payment-detail-edit-dialog">
    <el-form :model="paymentDetailModel" :rules="rules" ref="formRef" label-width="120px" class="payment-detail-edit-form">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="缴费对象" prop="paymentObjectId">
            <div class="object-selector">
              <el-input v-model="objectDisplay" placeholder="请选择缴费对象" readonly style="width: calc(100% - 100px);" />
              <el-button type="primary" @click="openObjectSelector" style="margin-left: 10px;">选择对象</el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属小区" prop="communityId">
            <el-select v-model="paymentDetailModel.communityId" placeholder="请选择小区" style="width: 100%;" filterable>
              <el-option 
                v-for="item in communityList" 
                :key="item.id" 
                :label="item.communityName" 
                :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="缴费项目快照" prop="paymentItemSnapshot">
            <el-input v-model="paymentDetailModel.paymentItemSnapshot" maxlength="200" placeholder="请输入缴费项目快照" clearable />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="单价" prop="unitPrice">
            <el-input-number v-model="paymentDetailModel.unitPrice" :min="0" :precision="2" 
              style="width: 100%;" placeholder="请输入单价" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单位" prop="unit">
            <el-input v-model="paymentDetailModel.unit" maxlength="20" placeholder="如：度、平方米" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数量" prop="quantity">
            <el-input-number v-model="paymentDetailModel.quantity" :min="0" :precision="2" 
              style="width: 100%;" placeholder="请输入数量" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="折扣" prop="discount">
            <el-input-number v-model="paymentDetailModel.discount" :min="0" :max="10" :precision="1" 
              style="width: 100%;" placeholder="0-10折" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态" prop="status">
            <el-select v-model="paymentDetailModel.status" placeholder="请选择状态" style="width: 100%;">
              <el-option 
                v-for="item in statusOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计费周期" prop="billingCycle">
            <el-select v-model="paymentDetailModel.billingCycle" placeholder="请选择计费周期" style="width: 100%;">
              <el-option label="月度" value="monthly" />
              <el-option label="季度" value="quarterly" />
              <el-option label="年度" value="yearly" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="账单日" prop="billDay">
            <el-input v-model="paymentDetailModel.billDay" maxlength="50" 
              placeholder="格式：0_6_18:00（月度:每月6号18:00）" clearable />
            <div class="help-text">
              <small>格式说明：月度:每月6号18:00 → 0_6_18:00；季度:每季度开始第一个月的16号18:00 → 1_16_18:00；年度:每年7月30号18:00 → 7_30_18:00</small>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="生效日期" prop="effectiveDate">
            <el-date-picker v-model="paymentDetailModel.effectiveDate" type="date" 
              placeholder="请选择生效日期" style="width: 100%;" value-format="YYYY-MM-DD" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="失效日期" prop="expiringDate">
            <el-date-picker v-model="paymentDetailModel.expiringDate" type="date" 
              placeholder="请选择失效日期" style="width: 100%;" value-format="YYYY-MM-DD" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="备注" prop="note">
            <el-input v-model="paymentDetailModel.note" type="textarea" :rows="3" 
              maxlength="300" placeholder="请输入备注" show-word-limit />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 金额预览 -->
      <el-row :gutter="16" v-if="paymentDetailModel.unitPrice && paymentDetailModel.quantity">
        <el-col :span="24">
          <el-form-item label="金额预览">
            <div class="amount-preview">
              <span>单价：¥{{ paymentDetailModel.unitPrice }} × 数量：{{ paymentDetailModel.quantity }} × 折扣：{{ paymentDetailModel.discount || 10 }}折 = </span>
              <span style="color: #f56c6c; font-weight: bold; font-size: 16px;">¥{{ calculateAmount() }}</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <!-- 缴费对象选择弹窗 -->
    <el-dialog title="选择缴费对象" v-model="objectSelectorDialog" width="800px" append-to-body>
      <div class="object-search">
        <el-input v-model="objectSearchKeyword" placeholder="搜索对象名称或手机号" clearable style="width: 300px; margin-bottom: 16px;" @input="searchObjects" />
      </div>
      <el-table :data="objectList" @row-click="selectObject" style="cursor: pointer;" max-height="400">
        <el-table-column prop="moniker" label="对象名称" width="150" align="center"/>
        <el-table-column prop="phone" label="手机号" width="130" align="center"/>
        <el-table-column prop="email" label="邮箱" align="center" show-overflow-tooltip/>
        <el-table-column prop="address" label="地址" align="center" show-overflow-tooltip/>
        <el-table-column prop="residentId" label="关联住户" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.residentId" type="success" size="small">已关联</el-tag>
            <el-tag v-else type="info" size="small">未关联</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-col" style="margin-top: 16px;">
        <el-pagination background layout="prev, pager, next" @current-change="objectPageChange" 
          :total="objectTotal" :page-size="objectSearchModel.pageSize" :current-page="objectSearchModel.pageNum" />
      </div>
    </el-dialog>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { addPropertyPaymentDetail, editPropertyPaymentDetail, listPropertyPaymentObject } from '@/api/property/paymentItems'
import { listCommunity } from '@/api/community/community'
import { listDictByNameEn } from '@/api/system/dict'
import { getSelectedCommunityId } from '@/store/modules/options'
import mitt from '@/utils/mitt'

export default {
  name: 'paymentDetailEdit',
  data() {
    return {
      loading: false,
      paymentDetailModel: {
        id: undefined,
        paymentItemSnapshot: '',
        unitPrice: null,
        unit: '',
        quantity: null,
        discount: 10,
        status: '',
        note: '',
        communityId: null,
        paymentObjectId: null,
        billingCycle: '',
        billDay: '',
        effectiveDate: '',
        expiringDate: ''
      },
      dialog: {
        show: false,
        title: ''
      },
      objectDisplay: '', // 缴费对象显示信息
      objectSelectorDialog: false,
      objectSearchKeyword: '',
      objectList: [],
      objectTotal: 0,
      objectSearchModel: {
        pageNum: 1,
        pageSize: 10
      },
      communityList: [],
      statusOptions: [],
      rules: {
        paymentObjectId: [
          { required: true, message: '请选择缴费对象', trigger: 'change' }
        ],
        communityId: [
          { required: true, message: '请选择所属小区', trigger: 'change' }
        ],
        paymentItemSnapshot: [
          { required: true, message: '请输入缴费项目快照', trigger: 'blur' }
        ],
        unitPrice: [
          { required: true, message: '请输入单价', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请输入单位', trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: '请输入数量', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        billingCycle: [
          { required: true, message: '请选择计费周期', trigger: 'change' }
        ]
      }
    }
  },
  
  methods: {
    /**
     * 计算金额
     */
    calculateAmount() {
      const amount = (this.paymentDetailModel.unitPrice || 0) * (this.paymentDetailModel.quantity || 0) * (this.paymentDetailModel.discount || 10) / 10
      return amount.toFixed(2)
    },
    
    /**
     * 打开缴费对象选择器
     */
    openObjectSelector() {
      this.objectSelectorDialog = true
      this.searchObjects()
    },
    
    /**
     * 搜索缴费对象
     */
    searchObjects() {
      const params = {
        ...this.objectSearchModel
      }

      // 如果有搜索关键词，同时搜索对象名称和手机号
      if (this.objectSearchKeyword) {
        params.moniker = this.objectSearchKeyword
        params.phone = this.objectSearchKeyword
      }
      
      listPropertyPaymentObject(params).then(res => {
        this.objectList = res.data.data.list || []
        this.objectTotal = res.data.data.total || 0
      }).catch(err => {
        console.error('搜索缴费对象失败:', err)
      })
    },
    
    /**
     * 选择缴费对象
     */
    selectObject(object) {
      this.paymentDetailModel.paymentObjectId = object.id
      this.objectDisplay = `${object.moniker} (${object.phone || object.email || 'ID:' + object.id})`
      this.objectSelectorDialog = false
    },
    
    /**
     * 缴费对象分页变化
     */
    objectPageChange(num) {
      this.objectSearchModel.pageNum = num
      this.searchObjects()
    },
    
    /**
     * 加载小区列表
     */
    loadCommunityList() {
      listCommunity({ pageNum: 1, pageSize: 500 }).then(res => {
        this.communityList = res.data.data.list || []
      }).catch(err => {
        console.error('加载小区列表失败:', err)
      })
    },
    
    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        // 加载缴费状态字典
        const statusRes = await listDictByNameEn('payment_status')
        this.statusOptions = (statusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))
        
        // 如果字典为空，使用默认选项
        if (this.statusOptions.length === 0) {
          this.statusOptions = [
            { value: 'active', label: '有效' },
            { value: 'inactive', label: '无效' },
            { value: 'pending', label: '待处理' },
            { value: 'expired', label: '已过期' }
          ]
        }
      } catch (err) {
        console.error('加载字典数据失败:', err)
        // 使用默认状态选项
        this.statusOptions = [
          { value: 'active', label: '有效' },
          { value: 'inactive', label: '无效' },
          { value: 'pending', label: '待处理' },
          { value: 'expired', label: '已过期' }
        ]
      }
    },
    
    /**
     * 重置表单
     */
    resetForm() {
      this.$refs.formRef && this.$refs.formRef.resetFields()
      this.paymentDetailModel = {
        id: undefined,
        paymentItemSnapshot: '',
        unitPrice: null,
        unit: '',
        quantity: null,
        discount: 10,
        status: 'active',
        note: '',
        communityId: getSelectedCommunityId() || null,
        paymentObjectId: null,
        billingCycle: '',
        billDay: '',
        effectiveDate: '',
        expiringDate: ''
      }
      this.objectDisplay = ''
      this.objectSearchKeyword = ''
    },
    
    /**
     * 提交表单
     */
    submit() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return
        
        this.loading = true
        const api = this.paymentDetailModel.id ? editPropertyPaymentDetail : addPropertyPaymentDetail
        
        api(this.paymentDetailModel).then(() => {
          this.$message.success(this.paymentDetailModel.id ? '修改成功' : '添加成功')
          this.dialog.show = false
          this.$emit('search')
        }).catch(err => {
          this.$message.error(err.data?.errorMessage || '操作失败')
        }).finally(() => {
          this.loading = false
        })
      })
    }
  },
  
  async mounted() {
    await this.initDictData()
    this.loadCommunityList()
    
    mitt.on('openPaymentDetailAdd', () => {
      this.resetForm()
      this.dialog.title = '新增缴费明细'
      this.dialog.show = true
    })
    
    mitt.on('openPaymentDetailEdit', (data) => {
      this.resetForm()
      this.paymentDetailModel = { ...data }
      
      // 设置缴费对象显示信息
      if (data.paymentObjectId) {
        // 这里可以根据需要从缓存或重新查询获取对象信息
        this.objectDisplay = `对象ID: ${data.paymentObjectId}`
      }
      
      this.dialog.title = '编辑缴费明细'
      this.dialog.show = true
    })
  },
  
  beforeUnmount() {
    mitt.off('openPaymentDetailAdd')
    mitt.off('openPaymentDetailEdit')
  }
}
</script>

<style scoped>
.payment-detail-edit-dialog {
  border-radius: 8px;
}

.payment-detail-edit-form {
  padding: 0 16px;
}

.object-selector {
  display: flex;
  align-items: center;
}

.object-search {
  margin-bottom: 16px;
}

.amount-preview {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.help-text {
  margin-top: 4px;
  color: #909399;
}

.pagination-col {
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 深色主题适配 */
.dark-theme .payment-detail-edit-dialog {
  background-color: var(--card-background);
}

.dark-theme .amount-preview {
  background-color: var(--card-background);
  border-color: #444;
}
</style>
