# 工单详情页时间线实现总结

## 概述

工单详情页的时间线功能用于展示工单从创建到完成的整个处理过程，包括状态变更、操作人员、时间节点、备注信息和相关图片等。

## 数据结构

### 1. API返回的原始时间线数据

```javascript
// 从 workOrderApi.getWorkOrderDetail(id) 返回的数据结构
{
  "id": "工单ID",
  "timeLine": [
    {
      "id": "时间线项ID",
      "status": "工单状态英文名",
      "createTime": "2024-01-15T10:30:00",
      "personName": "物业人员姓名",
      "residentName": "住户姓名", 
      "note": "备注信息",
      "media": "image1.jpg,image2.png" // 逗号分隔的图片文件名
    }
  ]
}
```

### 2. 处理后的时间线数据

```javascript
// processTimeLine() 方法处理后的数据结构
processedTimeLine: [
  {
    "id": "时间线项ID",
    "status": "工单状态英文名",
    "statusText": "工单状态中文显示名",
    "createTime": "原始时间",
    "formattedTime": "格式化后的时间显示",
    "operatorName": "操作员姓名",
    "note": "备注信息",
    "media": "原始图片字符串",
    "imageList": ["image1.jpg", "image2.png"] // 处理后的图片数组
  }
]
```

## 核心实现方法

### 1. 时间线数据处理 (processTimeLine)

```javascript
processTimeLine: function(timeLine) {
  if (!timeLine || !Array.isArray(timeLine)) return [];

  return timeLine.map(item => {
    // 1. 确定操作员名称
    let operatorName = '系统';
    if (item.personName) {
      operatorName = item.personName;        // 物业人员
    } else if (item.residentName) {
      operatorName = item.residentName;      // 住户
    }

    // 2. 获取状态显示文本
    const statusText = this.getStatusName(item.status) || '状态变更';

    // 3. 处理图片字段 - 支持逗号分隔的多图
    const imageList = item.media ? 
      item.media.split(',').filter(img => img.trim()) : [];

    return {
      ...item,
      operatorName: operatorName,
      statusText: statusText,
      formattedTime: this.formatTimelineTime(item.createTime),
      imageList: imageList
    };
  }).sort((a, b) => new Date(b.createTime) - new Date(a.createTime)); // 按时间倒序
}
```

### 2. 时间格式化 (formatTimelineTime)

```javascript
formatTimelineTime: function(timeStr) {
  if (!timeStr) return '';

  const date = new Date(timeStr);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const itemDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  const diffDays = Math.floor((today - itemDate) / (1000 * 60 * 60 * 24));
  const timeFormat = date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });

  if (diffDays === 0) {
    return `今天 ${timeFormat}`;           // 今天 14:30
  } else if (diffDays === 1) {
    return `昨天 ${timeFormat}`;           // 昨天 14:30
  } else if (diffDays < 7) {
    return `${diffDays}天前 ${timeFormat}`; // 3天前 14:30
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });                                   // 01-15 14:30
  }
}
```

### 3. 状态名称映射 (getStatusName)

```javascript
getStatusName: function (statusEn) {
  // 从字典数据中获取状态的中文显示名
  const status = this.data.workOrderStatus.find(item => item.nameEn === statusEn);
  return status ? status.nameCn : statusEn;
}
```

## 前端展示实现

### 1. WXML模板结构

```xml
<!-- 处理进度时间线 -->
<view class="detail-card" wx:if="{{workOrder.processedTimeLine && workOrder.processedTimeLine.length > 0}}">
  <view class="card-title">处理进度</view>
  <view class="progress-timeline">
    <view
      wx:for="{{workOrder.processedTimeLine}}"
      wx:key="id"
      class="timeline-item {{index === 0 ? 'first' : ''}} {{index === workOrder.processedTimeLine.length - 1 ? 'last' : ''}}"
    >
      <!-- 时间线节点圆点 -->
      <view class="timeline-dot status-{{item.status}}"></view>
      
      <!-- 时间线内容 -->
      <view class="timeline-content">
        <view class="timeline-time">{{item.formattedTime}}</view>
        <view class="timeline-action">{{item.statusText}}</view>
        <view class="timeline-operator">操作人：{{item.operatorName}}</view>
        <view class="timeline-remark" wx:if="{{item.note}}">备注：{{item.note}}</view>

        <!-- 时间线图片 -->
        <view class="timeline-images" wx:if="{{item.imageList && item.imageList.length > 0}}">
          <view class="timeline-image-item" wx:for="{{item.imageList}}" wx:for-item="image" wx:for-index="imgIndex" wx:key="imgIndex">
            <image
              src="{{image.startsWith('http') ? image : (apiUrl + '/common-api/v1/file/' + image)}}"
              class="timeline-image"
              mode="aspectFill"
              bindtap="previewTimelineImage"
              data-index="{{imgIndex}}"
              data-images="{{item.imageList}}"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
```

### 2. CSS样式设计

```css
/* 时间线容器 */
.progress-timeline {
  position: relative;
}

/* 时间线项目 */
.timeline-item {
  position: relative;
  padding-left: 20px;
  padding-bottom: 20px;
}

/* 时间线连接线 */
.timeline-item::before {
  content: '';
  position: absolute;
  left: 6px;
  top: 8px;
  bottom: 0;
  width: 1px;
  background-color: #e8e8e8;
}

.timeline-item.last::before {
  display: none; /* 最后一项不显示连接线 */
}

/* 时间线节点圆点 */
.timeline-dot {
  position: absolute;
  left: 0;
  top: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #FF8C00;
}

/* 不同状态的圆点颜色 */
.timeline-dot.status-wait_process { background-color: #ff9800; }  /* 待处理 */
.timeline-dot.status-accepted { background-color: #2196f3; }      /* 已接受 */
.timeline-dot.status-processing { background-color: #2196f3; }    /* 处理中 */
.timeline-dot.status-pending { background-color: #ff9800; }       /* 待确认 */
.timeline-dot.status-completed { background-color: #4caf50; }     /* 已完成 */
.timeline-dot.status-cancelled { background-color: #9e9e9e; }     /* 已取消 */

/* 时间线内容区域 */
.timeline-content {
  padding-left: 12px;
}

.timeline-time {
  font-size: 14px;
  color: #999;
  margin-bottom: 4px;
}

.timeline-action {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.timeline-operator {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.timeline-remark {
  font-size: 14px;
  color: #666;
  background-color: #f5f7fa;
  padding: 8px;
  border-radius: 4px;
}

/* 时间线图片 */
.timeline-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.timeline-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
}
```

## 图片处理机制

### 1. 图片数据格式

- **输入格式**: `"image1.jpg,image2.png,image3.jpeg"` (逗号分隔字符串)
- **处理后**: `["image1.jpg", "image2.png", "image3.jpeg"]` (数组)

### 2. 图片URL构建

```javascript
// 在WXML中动态构建完整URL
src="{{image.startsWith('http') ? image : (apiUrl + '/common-api/v1/file/' + image)}}"

// 支持两种格式：
// 1. 相对路径: "image1.jpg" -> "https://api.domain.com/common-api/v1/file/image1.jpg"
// 2. 完整URL: "https://cdn.domain.com/image1.jpg" -> "https://cdn.domain.com/image1.jpg"
```

### 3. 图片预览功能

```javascript
previewTimelineImage(e) {
  const { index, images } = e.currentTarget.dataset;
  const { apiUrl } = this.data;

  // 构建完整的图片URL数组
  const urls = images.map(img => {
    return img.startsWith('http') ? img : `${apiUrl}/common-api/v1/file/${img}`;
  });

  wx.previewImage({
    current: urls[index],
    urls: urls
  });
}
```

## 字典数据依赖

### 1. 工单状态字典 (work_order_status)

```javascript
// 从 util.getDictByNameEn('work_order_status')[0].children 获取
[
  { nameEn: "wait_process", nameCn: "待处理" },
  { nameEn: "accepted", nameCn: "已接受" },
  { nameEn: "processing", nameCn: "处理中" },
  { nameEn: "pending", nameCn: "待确认" },
  { nameEn: "completed", nameCn: "已完成" },
  { nameEn: "cancelled", nameCn: "已取消" }
]
```

## 后台网页端实现建议

### 1. 数据库表结构

```sql
-- 工单时间线表
CREATE TABLE work_order_timeline (
  id BIGINT PRIMARY KEY,
  work_order_id BIGINT NOT NULL,           -- 工单ID
  status VARCHAR(50) NOT NULL,             -- 状态英文名
  operator_type VARCHAR(20),               -- 操作员类型: person/resident/system
  operator_id BIGINT,                      -- 操作员ID
  operator_name VARCHAR(100),              -- 操作员姓名
  note TEXT,                               -- 备注信息
  media TEXT,                              -- 图片文件名(逗号分隔)
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_work_order_id (work_order_id)
);
```

### 2. 后端API接口

```javascript
// 获取工单时间线
GET /api/work-orders/{id}/timeline

// 返回数据格式
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "status": "wait_process",
      "operatorType": "system",
      "operatorName": "系统",
      "note": "工单已创建",
      "media": "",
      "createTime": "2024-01-15T10:30:00"
    },
    {
      "id": 2,
      "status": "accepted",
      "operatorType": "person",
      "operatorName": "张师傅",
      "note": "已接受工单，准备上门处理",
      "media": "before1.jpg,before2.jpg",
      "createTime": "2024-01-15T11:00:00"
    }
  ]
}
```

### 3. 前端组件实现 (Vue/React)

```vue
<template>
  <div class="timeline-container">
    <div 
      v-for="(item, index) in timelineData" 
      :key="item.id"
      :class="['timeline-item', { 'last': index === timelineData.length - 1 }]"
    >
      <div :class="['timeline-dot', `status-${item.status}`]"></div>
      <div class="timeline-content">
        <div class="timeline-time">{{ formatTime(item.createTime) }}</div>
        <div class="timeline-action">{{ getStatusText(item.status) }}</div>
        <div class="timeline-operator">操作人：{{ item.operatorName }}</div>
        <div v-if="item.note" class="timeline-remark">备注：{{ item.note }}</div>
        
        <!-- 图片展示 -->
        <div v-if="item.imageList?.length" class="timeline-images">
          <img 
            v-for="(image, imgIndex) in item.imageList"
            :key="imgIndex"
            :src="getImageUrl(image)"
            class="timeline-image"
            @click="previewImage(image, item.imageList)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    workOrderId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      timelineData: []
    }
  },
  methods: {
    async loadTimeline() {
      const response = await fetch(`/api/work-orders/${this.workOrderId}/timeline`);
      const result = await response.json();
      
      this.timelineData = result.data.map(item => ({
        ...item,
        imageList: item.media ? item.media.split(',').filter(img => img.trim()) : []
      })).sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
    },
    
    formatTime(timeStr) {
      // 时间格式化逻辑，与小程序端保持一致
      const date = new Date(timeStr);
      const now = new Date();
      const diffDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));
      
      if (diffDays === 0) return `今天 ${date.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'})}`;
      if (diffDays === 1) return `昨天 ${date.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'})}`;
      if (diffDays < 7) return `${diffDays}天前 ${date.toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'})}`;
      
      return date.toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    
    getStatusText(status) {
      const statusMap = {
        'wait_process': '待处理',
        'accepted': '已接受',
        'processing': '处理中',
        'pending': '待确认',
        'completed': '已完成',
        'cancelled': '已取消'
      };
      return statusMap[status] || status;
    },
    
    getImageUrl(imageName) {
      if (imageName.startsWith('http')) return imageName;
      return `${process.env.VUE_APP_API_URL}/common-api/v1/file/${imageName}`;
    },
    
    previewImage(current, imageList) {
      // 实现图片预览功能
      const urls = imageList.map(img => this.getImageUrl(img));
      // 可以使用 element-ui 的 el-image-viewer 或其他图片预览组件
    }
  },
  
  mounted() {
    this.loadTimeline();
  }
}
</script>

<style scoped>
/* CSS样式与小程序端保持一致，适配web端 */
.timeline-container {
  position: relative;
}

.timeline-item {
  position: relative;
  padding-left: 20px;
  padding-bottom: 20px;
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 6px;
  top: 8px;
  bottom: 0;
  width: 1px;
  background-color: #e8e8e8;
}

.timeline-item.last::before {
  display: none;
}

.timeline-dot {
  position: absolute;
  left: 0;
  top: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #FF8C00;
}

/* 状态颜色 */
.timeline-dot.status-wait_process { background-color: #ff9800; }
.timeline-dot.status-accepted { background-color: #2196f3; }
.timeline-dot.status-processing { background-color: #2196f3; }
.timeline-dot.status-pending { background-color: #ff9800; }
.timeline-dot.status-completed { background-color: #4caf50; }
.timeline-dot.status-cancelled { background-color: #9e9e9e; }

.timeline-content {
  padding-left: 12px;
}

.timeline-time {
  font-size: 14px;
  color: #999;
  margin-bottom: 4px;
}

.timeline-action {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.timeline-operator {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.timeline-remark {
  font-size: 14px;
  color: #666;
  background-color: #f5f7fa;
  padding: 8px;
  border-radius: 4px;
  margin-top: 8px;
}

.timeline-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.timeline-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s;
}

.timeline-image:hover {
  transform: scale(1.05);
}
</style>
```

## 总结

工单时间线的核心实现包括：

1. **数据处理**: 将API返回的原始时间线数据进行格式化和增强
2. **时间格式化**: 提供人性化的时间显示（今天、昨天、N天前等）
3. **状态映射**: 将英文状态码转换为中文显示文本
4. **图片处理**: 支持逗号分隔的多图格式，动态构建完整URL
5. **视觉设计**: 使用圆点、连接线、不同颜色表示不同状态的时间线
6. **交互功能**: 支持图片预览、点击放大等用户交互

这套实现方案可以直接应用到后台网页端，只需要适配相应的前端框架和组件库即可。
