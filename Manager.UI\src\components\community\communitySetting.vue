<template>
  <el-dialog
    :title="dialog.title"
    v-model="dialog.show"
    width="900px"
    top="5vh"
    :close-on-click-modal="false"
    class="community-setting-dialog"
  >
    <div class="setting-container">
      <el-tabs v-model="activeTab" type="card" class="setting-tabs">
        <el-tab-pane label="物业服务设置" name="property">
          <div class="property-setting-content" v-if="activeTab === 'property'">
            <!-- 服务参数设置 -->
            <div class="setting-section">
              <h3 class="section-title">服务参数设置</h3>
              <div class="service-params">
                <div v-for="(service, index) in serviceParams" :key="index" class="service-item">
                  <div class="service-header">
                    <el-select v-model="service.type" placeholder="请选择服务类型" style="width: 200px;">
                      <el-option label="服务" value="service" />
                      <el-option label="维修" value="repair" />
                      <el-option label="投诉" value="complaint" />
                      <el-option label="建议" value="suggestion" />
                    </el-select>
                    <el-button type="danger" size="small" @click="removeService(index)" v-if="serviceParams.length > 1">
                      删除
                    </el-button>
                  </div>
                  
                  <div class="time-settings">
                    <div v-for="(time, timeIndex) in service.time" :key="timeIndex" class="time-item">
                      <div class="time-row">
                        <el-form-item label="级别:" label-width="60px">
                          <el-select v-model="time.level" placeholder="级别" style="width: 100px;">
                            <el-option label="1级" :value="1" />
                            <el-option label="2级" :value="2" />
                            <el-option label="3级" :value="3" />
                            <el-option label="4级" :value="4" />
                          </el-select>
                        </el-form-item>
                        
                        <el-form-item label="响应时间:" label-width="80px">
                          <el-select v-model="time.resTime" placeholder="响应时间" style="width: 120px;">
                            <el-option label="30分钟" :value="30" />
                            <el-option label="60分钟" :value="60" />
                            <el-option label="120分钟" :value="120" />
                            <el-option label="240分钟" :value="240" />
                          </el-select>
                        </el-form-item>
                        
                        <el-form-item label="到达时间:" label-width="80px">
                          <el-select v-model="time.arriveTime" placeholder="到达时间" style="width: 120px;">
                            <el-option label="30分钟" :value="30" />
                            <el-option label="60分钟" :value="60" />
                            <el-option label="90分钟" :value="90" />
                            <el-option label="120分钟" :value="120" />
                          </el-select>
                        </el-form-item>
                        
                        <el-form-item label="完成时间:" label-width="80px">
                          <el-select v-model="time.completeTime" placeholder="完成时间" style="width: 120px;">
                            <el-option label="60分钟" :value="60" />
                            <el-option label="120分钟" :value="120" />
                            <el-option label="240分钟" :value="240" />
                            <el-option label="480分钟" :value="480" />
                          </el-select>
                        </el-form-item>
                        
                        <el-button type="danger" size="small" @click="removeTimeItem(index, timeIndex)" v-if="service.time.length > 1">
                          删除
                        </el-button>
                      </div>
                    </div>
                    
                    <el-button type="primary" size="small" @click="addTimeItem(index)">
                      添加时间设置
                    </el-button>
                  </div>
                </div>
                
                <el-button type="primary" @click="addService">添加服务类型</el-button>
              </div>
            </div>
            
            <!-- 工作时间段设置 -->
            <div class="setting-section">
              <h3 class="section-title">工作时间段设置</h3>
              <div class="working-hours">
                <div v-for="(hour, index) in workingHours" :key="index" class="hour-item">
                  <el-time-picker
                    v-model="hour.start"
                    format="HH:mm"
                    value-format="HH:mm"
                    placeholder="开始时间"
                    style="width: 120px; margin-right: 10px;"
                  />
                  <span>-</span>
                  <el-time-picker
                    v-model="hour.end"
                    format="HH:mm"
                    value-format="HH:mm"
                    placeholder="结束时间"
                    style="width: 120px; margin: 0 10px;"
                  />
                  <el-button type="danger" size="small" @click="removeWorkingHour(index)" v-if="workingHours.length > 1">
                    删除
                  </el-button>
                </div>
                
                <el-button type="primary" @click="addWorkingHour">添加时间段</el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="供应商设置" name="supplier">
          <div class="supplier-setting-content">
            <el-empty description="供应商设置功能开发中..." />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { editCommunity } from '@/api/community/community'
import mitt from '@/utils/mitt'

export default {
  name: 'CommunitySetting',
  data() {
    return {
      dialog: {
        show: false,
        title: '小区设置'
      },
      loading: false,
      activeTab: 'property',
      currentCommunity: null,
      
      // 服务参数
      serviceParams: [],

      // 工作时间段
      workingHours: []
    }
  },
  
  methods: {
    /**
     * 添加服务类型
     */
    addService() {
      this.serviceParams.push({
        type: '',
        time: []
      })
    },
    
    /**
     * 删除服务类型
     */
    removeService(index) {
      this.serviceParams.splice(index, 1)
    },
    
    /**
     * 添加时间设置
     */
    addTimeItem(serviceIndex) {
      this.serviceParams[serviceIndex].time.push({
        level: null,
        resTime: null,
        arriveTime: null,
        completeTime: null
      })
    },
    
    /**
     * 删除时间设置
     */
    removeTimeItem(serviceIndex, timeIndex) {
      this.serviceParams[serviceIndex].time.splice(timeIndex, 1)
    },
    
    /**
     * 添加工作时间段
     */
    addWorkingHour() {
      this.workingHours.push({ start: '09:00', end: '17:00' })
    },
    
    /**
     * 删除工作时间段
     */
    removeWorkingHour(index) {
      this.workingHours.splice(index, 1)
    },
    
    /**
     * 重置表单
     */
    resetForm() {
      this.activeTab = 'property'
      this.serviceParams = []
      this.workingHours = []
    },
    
    /**
     * 解析扩展参数
     */
    parseExtendParams(expandData) {
      if (!expandData) return
      
      try {
        const params = JSON.parse(expandData)
        if (params.property) {
          // 解析服务参数
          if (params.property.serviceValidity) {
            this.serviceParams = params.property.serviceValidity
          }
          
          // 解析工作时间
          if (params.property.workingHours) {
            this.workingHours = params.property.workingHours.map(hour => {
              const [start, end] = hour.split('-')
              return { start, end }
            })
          }
        }
      } catch (error) {
        console.error('解析扩展参数失败:', error)
      }
    },
    
    /**
     * 验证设置数据
     */
    validateSettings() {
      // 验证服务参数
      for (let i = 0; i < this.serviceParams.length; i++) {
        const service = this.serviceParams[i]
        if (!service.type) {
          this.$message.warning(`请选择第${i + 1}个服务的类型`)
          return false
        }

        for (let j = 0; j < service.time.length; j++) {
          const time = service.time[j]
          if (!time.level || !time.resTime || !time.arriveTime || !time.completeTime) {
            this.$message.warning(`请完善第${i + 1}个服务的第${j + 1}个时间设置`)
            return false
          }
        }
      }

      // 验证工作时间
      for (let i = 0; i < this.workingHours.length; i++) {
        const hour = this.workingHours[i]
        if (!hour.start || !hour.end) {
          this.$message.warning(`请完善第${i + 1}个工作时间段`)
          return false
        }

        if (hour.start >= hour.end) {
          this.$message.warning(`第${i + 1}个工作时间段的开始时间不能晚于或等于结束时间`)
          return false
        }
      }

      return true
    },

    /**
     * 提交设置
     */
    submit() {
      if (!this.currentCommunity) return

      // 验证数据
      if (!this.validateSettings()) return

      this.loading = true

      // 构建设置数据
      const settingData = {
        property: {
          serviceValidity: this.serviceParams,
          workingHours: this.workingHours.map(hour => `${hour.start}-${hour.end}`)
        }
      }

      // 更新小区信息
      const updateData = {
        ...this.currentCommunity,
        expandData: JSON.stringify(settingData)
      }

      editCommunity(updateData)
        .then(() => {
          this.$message.success('设置保存成功')
          this.dialog.show = false
          this.$emit('search')
        })
        .catch(err => {
          this.$message.error(err.data?.errorMessage || '保存失败')
        })
        .finally(() => {
          this.loading = false
        })
    }
  },
  
  mounted() {
    mitt.on('openCommunitySetting', (data) => {
      this.resetForm()
      this.currentCommunity = data
      this.dialog.title = `${data.communityName} - 设置`
      
      // 解析现有的扩展参数
      this.parseExtendParams(data.expandData)
      
      this.dialog.show = true
    })
  },
  
  beforeUnmount() {
    mitt.off('openCommunitySetting')
  }
}
</script>

<style scoped>
.community-setting-dialog {
  border-radius: 8px;
}

.setting-container {
  padding: 0 16px;
}

.setting-tabs {
  margin-bottom: 20px;
}

.setting-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.service-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 6px;
  border: 1px solid #dcdfe6;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.time-settings {
  margin-top: 15px;
}

.time-item {
  margin-bottom: 10px;
}

.time-row {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.hour-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.working-hours {
  background-color: #fff;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #dcdfe6;
}

.supplier-setting-content {
  padding: 40px 0;
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 深色主题适配 */
.dark-theme .setting-section {
  background-color: var(--card-background);
  border-color: #444;
}

.dark-theme .service-item,
.dark-theme .working-hours {
  background-color: var(--card-background);
  border-color: #444;
}

.dark-theme .section-title {
  color: #e5eaf3;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .time-row {
    flex-direction: column;
    gap: 10px;
  }

  .service-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .hour-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>
