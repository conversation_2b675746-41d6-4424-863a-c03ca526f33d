# 菜单缓存问题修复

## 🔍 问题分析

用户反馈：在权限变更后，刷新页面时菜单列表仍然显示旧的权限数据，怀疑 `onLoad` 函数中的 `load-menu` 接口存在缓存问题。

### 问题原因

1. **本地存储恢复**：页面刷新时从本地存储恢复旧的权限数据，而不是重新获取
2. **浏览器缓存**：`load-menu` 接口可能被浏览器缓存，导致返回旧数据
3. **服务端缓存**：后端可能对菜单数据进行了缓存

## ✅ 修复方案

### 1. API 层面防缓存

#### 修复 `loadMenu` API
```javascript
// src/api/system/menu.js
export const loadMenu = () =>
	request({
		url: '/manage-api/v1/menu/load-menu',
		method: 'get',
		params: {
			_t: Date.now() // 添加时间戳防止缓存
		},
		headers: {
			'Cache-Control': 'no-cache',
			'Pragma': 'no-cache'
		}
	})
```

#### 修复 `currentUser` API
```javascript
// src/api/system/user.js
export const currentUser = () =>
    request({
        url: '/manage-api/v1/user/current',
        method: 'get',
        params: {
            _t: Date.now() // 添加时间戳防止缓存
        },
        headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
    })
```

### 2. 增强调试信息

#### 优化 `onLoad` 函数日志
```javascript
// src/store/modules/user.js
async function onLoad(toPath) {
	console.log('🔄 onLoad 开始执行，toPath:', toPath, '时间戳:', new Date().toISOString())

	try {
		console.log('🌐 开始获取用户信息和权限...')
		const [user_res, permission_res] = await Promise.all([
			currentUser(),
			loadMenu()
		])
		console.log('✅ 用户信息和权限获取成功:', { 
			user: user_res.data.data, 
			permissions: permission_res.data.data,
			permissionCount: permission_res.data.data?.length || 0,
			timestamp: new Date().toISOString()
		})
		// ...
	}
}
```

### 3. 页面刷新时重新获取菜单

#### 修改初始化逻辑
```javascript
// src/store/modules/user.js
// 初始化时重新获取最新的用户信息和菜单（确保每次刷新都是最新数据）
setTimeout(async () => {
	const token = window.$local?.get('smartPropertyToken')

	console.log('🚀 应用启动检查:', { hasToken: !!token })

	// 如果有token，重新获取最新的用户信息和菜单
	if (token) {
		try {
			console.log('🔄 页面刷新，重新获取最新用户信息和菜单...')
			await onLoad(false) // 不跳转，只加载数据
			console.log('✅ 页面刷新时重新加载用户信息和菜单完成')
		} catch (error) {
			console.error('❌ 页面刷新时重新加载失败:', error)
			// 如果重新加载失败，可能是token过期，清除本地数据
			window.$local?.remove('smartPropertyToken')
			window.$local?.remove('frontPermissions')
			window.$local?.remove('smartPropertyUserInfo')
			window.$local?.remove('treePermissions')
		}
	}
}, 50)
```

## 🔧 核心改进

### 简化的解决方案

**原来的问题**：页面刷新时从本地存储恢复旧的权限数据
**现在的解决**：每次页面刷新都重新从服务器获取最新的用户信息和菜单

### 自动生效

现在不需要手动调用任何函数，每次页面刷新都会：
1. 检查是否有有效的token
2. 如果有token，自动调用 `onLoad(false)` 重新获取最新数据
3. 如果获取失败（如token过期），自动清除本地数据

### 在权限管理页面中的使用
```javascript
// 在角色权限修改后，只需要提示用户刷新页面即可
async handlePermissionChange() {
    try {
        // 保存权限变更
        await savePermissions()

        this.$message.success('权限更新成功，请刷新页面查看最新菜单')
    } catch (error) {
        this.$message.error('权限更新失败')
    }
}
```

## 🔍 调试方法

### 1. 检查网络请求
打开浏览器开发者工具 → Network 标签页：
- 查看 `load-menu` 请求是否带有时间戳参数
- 检查请求头是否包含 `Cache-Control: no-cache`
- 确认每次刷新都有新的网络请求

### 2. 检查控制台日志
查看控制台输出：
```
🔄 onLoad 开始执行，toPath: xxx 时间戳: 2025-06-27T06:24:27.440Z
🌐 开始获取用户信息和权限...
✅ 用户信息和权限获取成功: { permissionCount: 15, timestamp: ... }
```

### 3. 检查本地存储
在控制台执行：
```javascript
// 查看存储的权限数据
console.log('frontPermissions:', window.$local?.get('frontPermissions'))
console.log('treePermissions:', window.$local?.get('treePermissions'))

// 手动清除缓存
window.$local?.remove('frontPermissions')
window.$local?.remove('treePermissions')
```

## 🎯 预期效果

修复后的效果：
1. **每次刷新都获取最新数据**：页面刷新时自动重新调用 `loadMenu`
2. **权限变更立即生效**：刷新页面即可看到最新菜单
3. **详细的调试信息**：便于排查问题
4. **简单直接**：不需要手动调用任何函数，自动生效

## 📋 测试步骤

1. **修改用户权限**：在后台修改某个用户的菜单权限
2. **刷新页面**：在前端刷新页面，检查菜单是否更新
3. **检查网络请求**：确认 `load-menu` 请求带有新的时间戳
4. **检查控制台**：查看权限数量和时间戳是否为最新
5. **强制刷新测试**：调用 `forceReloadMenu()` 确认强制刷新功能

## 🚨 注意事项

1. **时间戳参数**：确保每次请求都有唯一的时间戳
2. **缓存头设置**：`Cache-Control` 和 `Pragma` 头必须正确设置
3. **本地存储清理**：强制刷新时要清除相关的本地存储
4. **错误处理**：网络请求失败时要有适当的错误处理

现在菜单缓存问题应该得到彻底解决！
