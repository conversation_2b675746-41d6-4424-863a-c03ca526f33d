import { createApp } from 'vue'
import App from './App.vue'

import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { useStore } from "@/store/lcAssign";

import JsonEditorVue from 'json-editor-vue3'


// 先初始化 Pinia
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

const app = createApp(App)
app.use(pinia)
app.use(JsonEditorVue)
// 创建 store 实例并挂载到全局
const lcStore = useStore();
app.config.globalProperties.$local = lcStore
window.$local = lcStore

// 保持原有的 $lcStore 兼容性
app.config.globalProperties.$lcStore = lcStore
window.$lcStore = lcStore

// 现在可以安全地导入依赖 window.$local 的模块
import router from '@/router/index'
import store from '@/store/index'

// 初始化 Token 监控系统
import { tokenMonitor } from '@/utils/tokenMonitor.js'
console.log('🔍 Token 监控系统已初始化')

// 在开发环境中加载测试工具
if (import.meta.env.DEV) {
    import('@/utils/tokenTest.js').then(({ tokenTester }) => {
        window.tokenTester = tokenTester;
        console.log('🧪 Token 测试工具已加载 (开发环境)');
    });
}

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 导入全局样式
import '@/assets/styles/global.css'

// 导入小区变化监听全局混入
import communityMixin from '@/mixins/communityMixin.js'

import { Base64 } from 'js-base64';

// 全局注册小区变化监听混入
app.mixin(communityMixin)

// 设置全局配置，关闭警告信息
app.config.warnHandler = () => {};
app.config.globalProperties.$Base64 = Base64

app.use(router)
app.use(store)
app.use(ElementPlus)

app.mount('#app')
