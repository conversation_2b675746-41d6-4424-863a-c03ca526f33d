<template>
	<el-container class="main-container" style="height: 100vh;">
		<!-- 顶部导航栏 -->
		<el-header class="app-header">
			<!-- 左侧标题和菜单按钮 -->
			<div class="header-left">
				<!-- 移动端菜单按钮 -->
				<el-button
					class="menu-toggle-btn"
					@click="drawerVisible = true"
					type="text"
					:icon="Menu"
					aria-label="打开菜单"
				></el-button>

				<div class="header-title">
					<h2>管理系统</h2>
				</div>
			</div>

			<!-- 右侧用户信息和主题切换 -->
			<div class="header-right">
				<!-- 主题切换 -->
				<ThemeSwitcher class="mr-3" />

				<!-- 小区选择下拉框 -->
				<div class="community-selector">
					<el-select
						v-model="selectedCommunityId"
						placeholder="请选择小区"
						filterable
						@change="onCommunityChange"
						:loading="communityLoading"
						class="community-select"
					>
						<el-option
							v-for="community in communityList"
							:key="community.id"
							:label="community.communityName"
							:value="community.id"
						/>
						<template #empty>
							<div class="empty-text">{{ communityList.length === 0 ? '暂无小区数据' : '未找到匹配的小区' }}</div>
						</template>
					</el-select>
				</div>

				<!-- 欢迎文本 -->
				<div class="welcome-text">
					<span class="welcome-label">欢迎您：</span>
					<span class="username">{{ username }} <span class="user-role">({{ userRole }})</span></span>
				</div>

				<!-- 用户下拉菜单 -->
				<el-dropdown trigger="hover" class="user-dropdown">
					<el-avatar
						:size="40"
						:src="userAvatarUrl || 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'"
						class="user-avatar"
					/>
					<template #dropdown>
						<el-dropdown-menu>
							<el-dropdown-item>
								<el-button type="text" size="small" @click="openPersonalInfo">个人信息</el-button>
							</el-dropdown-item>
							<el-dropdown-item>
								<el-button type="text" size="small" @click="openChangePassword">修改密码</el-button>
							</el-dropdown-item>
							<el-dropdown-item>
								<el-button
									@click="onLoginOut"
									type="text"
									size="small"
									class="logout-button"
								>退出登录</el-button>
							</el-dropdown-item>
						</el-dropdown-menu>
					</template>
				</el-dropdown>
			</div>
		</el-header>

		<!-- 主内容区域 -->
		<el-container class="content-wrapper">
			<!-- PC端侧边栏 -->
			<el-aside width="200px" class="sidebar desktop-sidebar">
				<el-scrollbar>
					<el-menu
						class="sidebar-menu"
						text-color="#fff"
						background-color="#314157"
						:default-openeds="0"
						:default-active="active"
						@select="selectMenu"
						:router="true"
						unique-opened
					>
						<!-- 菜单项循环 -->
						<template v-for="(item, index) in routerList" :key="index">
							<!-- 有子菜单的项目 -->
							<el-sub-menu
								:key="index"
								:index="index + ''"
								v-if="item.children && item.children.length > 0"
							>
								<template #title>
									<el-icon v-if="item.icon" class="menu-icon">
										<component :is="getIconComponent(item.icon)" />
									</el-icon>
									<el-icon v-else class="menu-icon menu-icon-placeholder"></el-icon>
									<span>{{ item.menuName }}</span>
								</template>
								<el-menu-item
									v-for="route in item.children"
									:key="route.id"
									:index="route.path"
								>
									<el-icon v-if="route.icon" class="submenu-icon">
										<component :is="getIconComponent(route.icon)" />
									</el-icon>
									<el-icon v-else class="submenu-icon submenu-icon-placeholder"></el-icon>
									{{ route.menuName }}
								</el-menu-item>
							</el-sub-menu>

							<!-- 无子菜单的项目 -->
							<el-menu-item
								v-else
								:index="item.path"
							>
								<el-icon v-if="item.icon" class="menu-icon">
									<component :is="getIconComponent(item.icon)" />
								</el-icon>
								<el-icon v-else class="menu-icon menu-icon-placeholder"></el-icon>
								{{ item.menuName }}
							</el-menu-item>
						</template>
					</el-menu>
				</el-scrollbar>
			</el-aside>

			<!-- 移动端抽屉菜单 -->
			<el-drawer
				v-model="drawerVisible"
				direction="ltr"
				:with-header="false"
				size="80%"
				class="mobile-drawer"
				:close-on-press-escape="true"
				:close-on-click-modal="true"
			>
				<el-scrollbar>
					<el-menu
						class="sidebar-menu"
						text-color="#fff"
						background-color="#314157"
						:default-openeds="0"
						:default-active="active"
						@select="handleMobileMenuSelect"
						:router="true"
						unique-opened
					>
						<!-- 菜单项循环 -->
						<template v-for="(item, index) in routerList" :key="index">
							<!-- 有子菜单的项目 -->
							<el-sub-menu
								:key="index"
								:index="index + ''"
								v-if="item.children && item.children.length > 0"
							>
								<template #title>
									<el-icon v-if="item.icon" class="menu-icon">
										<component :is="getIconComponent(item.icon)" />
									</el-icon>
									<el-icon v-else class="menu-icon menu-icon-placeholder"></el-icon>
									<span>{{ item.menuName }}</span>
								</template>
								<el-menu-item
									v-for="route in item.children"
									:key="route.id"
									:index="route.path"
								>
									<el-icon v-if="route.icon" class="submenu-icon">
										<component :is="getIconComponent(route.icon)" />
									</el-icon>
									<el-icon v-else class="submenu-icon submenu-icon-placeholder"></el-icon>
									{{ route.menuName }}
								</el-menu-item>
							</el-sub-menu>

							<!-- 无子菜单的项目 -->
							<el-menu-item
								v-else
								:index="item.path"
							>
								<el-icon v-if="item.icon" class="menu-icon">
									<component :is="getIconComponent(item.icon)" />
								</el-icon>
								<el-icon v-else class="menu-icon menu-icon-placeholder"></el-icon>
								{{ item.menuName }}
							</el-menu-item>
						</template>
					</el-menu>
				</el-scrollbar>
			</el-drawer>

			<!-- 主内容区 -->
			<el-main class="main-content">
				<div class="content-container">
					<router-view class="router-view-container"></router-view>
				</div>
			</el-main>
		</el-container>
	</el-container>

	<!-- 个人信息弹窗 -->
	<el-dialog
		v-model="personalInfoDialog.visible"
		:title="personalInfoDialog.title"
		width="600px"
		:close-on-click-modal="false"
	>
		<el-form
			:model="personalInfoForm"
			:rules="personalInfoRules"
			ref="personalInfoFormRef"
			label-width="100px"
		>
			<el-form-item label="用户名" prop="userName">
				<el-input v-model="personalInfoForm.userName" disabled />
			</el-form-item>
			<el-form-item label="昵称" prop="nickName">
				<el-input v-model="personalInfoForm.nickName" placeholder="请输入昵称" />
			</el-form-item>
			<el-form-item label="手机号" prop="phone">
				<el-input v-model="personalInfoForm.phone" placeholder="请输入手机号" />
			</el-form-item>
			<el-form-item label="邮箱" prop="email">
				<el-input v-model="personalInfoForm.email" placeholder="请输入邮箱" />
			</el-form-item>
			<el-form-item label="性别" prop="gender">
				<el-select v-model="personalInfoForm.gender" placeholder="请选择性别" style="width: 100%;">
					<el-option
						v-for="item in genderOptions"
						:key="item.nameEn"
						:label="item.nameCn"
						:value="item.nameEn"
					/>
				</el-select>
			</el-form-item>
			<el-form-item label="头像" prop="avatarUrl">
				<div>
					<el-upload
						class="avatar-uploader"
						:action="uploadUrl"
						:headers="uploadHeaders"
						:show-file-list="false"
						:on-success="handleAvatarSuccess"
						:before-upload="beforeAvatarUpload"
						accept="image/*"
					>
						<img v-if="personalInfoForm.avatarUrl" :src="getImageUrl(personalInfoForm.avatarUrl)" class="avatar" />
						<el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
					</el-upload>
					<div class="upload-tip">支持 jpg、png 格式，文件大小不超过 2MB</div>
				</div>
			</el-form-item>
			<el-form-item label="备注" prop="note">
				<el-input
					v-model="personalInfoForm.note"
					type="textarea"
					:rows="3"
					placeholder="请输入备注信息"
				/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="personalInfoDialog.visible = false">取消</el-button>
			<el-button type="primary" @click="savePersonalInfo">保存</el-button>
		</template>
	</el-dialog>

	<!-- 修改密码弹窗 -->
	<el-dialog
		v-model="changePasswordDialog.visible"
		:title="changePasswordDialog.title"
		width="500px"
		:close-on-click-modal="false"
	>
		<el-form
			:model="changePasswordForm"
			:rules="changePasswordRules"
			ref="changePasswordFormRef"
			label-width="100px"
		>
			<el-form-item label="旧密码" prop="oldPassword">
				<el-input
					v-model="changePasswordForm.oldPassword"
					type="password"
					placeholder="请输入旧密码"
					show-password
				/>
			</el-form-item>
			<el-form-item label="新密码" prop="newPassword">
				<el-input
					v-model="changePasswordForm.newPassword"
					type="password"
					placeholder="请输入新密码"
					show-password
				/>
			</el-form-item>
			<el-form-item label="确认密码" prop="confirmPassword">
				<el-input
					v-model="changePasswordForm.confirmPassword"
					type="password"
					placeholder="请再次输入新密码"
					show-password
				/>
			</el-form-item>
		</el-form>
		<template #footer>
			<el-button @click="changePasswordDialog.visible = false">取消</el-button>
			<el-button type="primary" @click="saveNewPassword">保存</el-button>
		</template>
	</el-dialog>
</template>

<script>
/**
 * 主布局组件
 *
 * 负责应用的整体布局，包括：
 * - 顶部导航栏
 * - 侧边菜单（PC端）
 * - 抽屉菜单（移动端）
 * - 主内容区域
 * - 响应式布局适配
 */
import {
	authLoginOut
} from '@/api/system/auth'
import { listCommunity } from '@/api/community/community'
import { updateCurrentUserInfo, updateCurrentUserPassword, currentUser, getUser } from '@/api/system/user'
import { listDictByNameEn } from '@/api/system/dict'
import {
	clearAllData,
	setCommunityList,
	setSelectedCommunity,
	getSelectedCommunity,
	getSelectedCommunityId
} from '@/store/modules/options'
import ThemeSwitcher from '@/components/ThemeSwitcher.vue'
import { Menu, Plus } from '@element-plus/icons-vue'
import { getIconComponent } from '@/utils/icon'

// 设备断点
const BREAKPOINTS = {
	MOBILE: 768
};

export default {
	name: 'AppLayout',

	components: {
		ThemeSwitcher,
		Plus
	},

	data() {
		return {
			// 路由菜单列表
			routerList: [],
			// 当前激活的菜单项
			active: '',
			// 抽屉菜单是否可见
			drawerVisible: false,
			// 是否为移动设备
			isMobile: false,
			// 窗口大小变化监听器
			resizeListener: null,
			// 小区相关数据
			communityList: [],
			selectedCommunityId: null,
			communityLoading: false,

			// 性别字典
			genderOptions: [],

			// 文件上传配置
			uploadUrl: import.meta.env.VITE_BASE_API + '/common-api/v1/file/upload',
			uploadHeaders: {},

			// 个人信息弹窗
			personalInfoDialog: {
				visible: false,
				title: '个人信息'
			},
			personalInfoForm: {
				userName: '',
				nickName: '',
				phone: '',
				email: '',
				gender: '',
				avatarUrl: '',
				note: ''
			},
			personalInfoRules: {
				nickName: [
					{ required: true, message: '请输入昵称', trigger: 'blur' }
				],
				phone: [
					{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
				],
				email: [
					{ type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
				],
				gender: [
					{ required: true, message: '请选择性别', trigger: 'change' }
				]
			},

			// 修改密码弹窗
			changePasswordDialog: {
				visible: false,
				title: '修改密码'
			},
			changePasswordForm: {
				oldPassword: '',
				newPassword: '',
				confirmPassword: ''
			},
			changePasswordRules: {
				oldPassword: [
					{ required: true, message: '请输入旧密码', trigger: 'blur' }
				],
				newPassword: [
					{ required: true, message: '请输入新密码', trigger: 'blur' },
					{ min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
				],
				confirmPassword: [
					{ required: true, message: '请再次输入新密码', trigger: 'blur' },
					{ validator: this.validateConfirmPassword, trigger: 'blur' }
				]
			}
		}
	},

	computed: {
		/**
		 * 获取当前用户名
		 * @returns {string} 当前用户名
		 */
		username() {
			// 从 Pinia 获取用户名
			const userName = window.$local?.get('userName')
			if (userName) return userName

			// 从 Pinia 获取用户信息
			const userInfo = window.$local?.get('smartPropertyUserInfo')
			return userInfo?.username || userInfo?.userName || 'system';
		},

		/**
		 * 获取当前用户角色
		 * @returns {string} 当前用户角色
		 */
		userRole() {
			// 从 Pinia 获取用户信息
			const userInfo = window.$local?.get('smartPropertyUserInfo')
			return userInfo?.roleName || userInfo?.role || '管理员';
		},

		/**
		 * 获取当前用户头像URL
		 * @returns {string} 用户头像URL
		 */
		userAvatarUrl() {
			// 从 Pinia 获取用户信息
			const userInfo = window.$local?.get('smartPropertyUserInfo')
			const avatarUrl = userInfo?.avatarUrl

			if (!avatarUrl) return ''

			// 如果已经是完整URL，直接返回
			if (avatarUrl.startsWith('http')) {
				return avatarUrl
			}

			// 否则拼接文件服务器地址
			return `${import.meta.env.VITE_BASE_API}/common-api/v1/file/${avatarUrl}`
		}
	},

	methods: {
		/**
		 * 加载性别字典
		 */
		async loadGenderOptions() {
			try {
				const res = await listDictByNameEn('gender')
				this.genderOptions = (res.data.data || []).map(item => ({
					nameCn: item.nameCn,
					nameEn: item.nameEn
				}))
			} catch (err) {
				console.error('加载性别字典失败:', err)
				// 使用默认性别选项
				this.genderOptions = [
					{ nameCn: '男', nameEn: 'male' },
					{ nameCn: '女', nameEn: 'female' },
					{ nameCn: '未知', nameEn: 'unknown' }
				]
			}
		},

		/**
		 * 获取图片完整URL
		 */
		getImageUrl(url) {
			if (!url) return ''
			if (url.startsWith('http')) return url
			return import.meta.env.VITE_BASE_API + '/common-api/v1/file/' + url
		},

		/**
		 * 头像上传成功回调
		 */
		handleAvatarSuccess(response) {
			console.log('头像上传响应:', response)

			// 参考其他页面的处理方式
			if (response && response.data) {
				this.personalInfoForm.avatarUrl = response.data
				this.$message.success('头像上传成功')
			} else if (response && response.code == 0) {
				this.personalInfoForm.avatarUrl = response.data
				this.$message.success('头像上传成功')
			} else {
				this.$message.error(response?.message || '头像上传失败')
			}
		},

		/**
		 * 头像上传前验证
		 */
		beforeAvatarUpload(file) {
			const isImage = file.type.startsWith('image/')
			const isLt2M = file.size / 1024 / 1024 < 2

			if (!isImage) {
				this.$message.error('只能上传图片文件!')
				return false
			}
			if (!isLt2M) {
				this.$message.error('图片大小不能超过 2MB!')
				return false
			}

			// 确保token是最新的
			this.initUploadHeaders()

			return true
		},

		/**
		 * 初始化上传头部
		 */
		initUploadHeaders() {
			try {
				const token = window.$local?.get('smartPropertyToken')
				if (token) {
					const tokenObj = JSON.parse(token)
					this.uploadHeaders.Authorization = tokenObj.access_token || token
				}
			} catch (e) {
				console.error('初始化上传请求头失败:', e)
				// 如果解析失败，尝试直接使用token
				const token = window.$local?.get('smartPropertyToken')
				if (token) {
					this.uploadHeaders.Authorization = token
				}
			}
		},

		/**
		 * 打开个人信息弹窗
		 */
		async openPersonalInfo() {
			// 先加载性别字典
			await this.loadGenderOptions()

			// 初始化上传头部
			this.initUploadHeaders()
			try {
				// 获取当前用户ID
				const userInfo = window.$local?.get('smartPropertyUserInfo')
				const userId = userInfo?.id || userInfo?.userId

				if (!userId) {
					this.$message.error('无法获取用户ID')
					return
				}

				// 使用getUser接口获取用户信息
				const res = await getUser(userId)
				if (res.data && res.data.data) {
					this.personalInfoForm = {
						userName: res.data.data.userName || '',
						nickName: res.data.data.nickName || '',
						phone: res.data.data.phone || '',
						email: res.data.data.email || '',
						gender: res.data.data.gender || '',
						avatarUrl: res.data.data.avatarUrl || '',
						note: res.data.data.note || ''
					}
				}
				this.personalInfoDialog.visible = true
			} catch (err) {
				this.$message.error('获取用户信息失败')
				console.error('获取用户信息失败:', err)
			}
		},

		/**
		 * 保存个人信息
		 */
		savePersonalInfo() {
			var that=this
			this.$refs.personalInfoFormRef.validate(valid => {
				if (!valid) return

				// 只提交允许修改的字段
				const updateData = {
					nickName: this.personalInfoForm.nickName,
					email: this.personalInfoForm.email,
					phone: this.personalInfoForm.phone,
					gender: this.personalInfoForm.gender,
					avatarUrl: this.personalInfoForm.avatarUrl,
					note: this.personalInfoForm.note
				}

				updateCurrentUserInfo(updateData)
					.then(async () => {
						this.$message.success('个人信息更新成功')
						this.personalInfoDialog.visible = false

						// 更新Pinia中的用户信息
						try {
							const res = await currentUser()
							if (res.data && res.data.data) {
								window.$local?.saveUserInfo(that.$Base64, res.data.data)
								console.log('用户信息已更新到Pinia')

								// 强制刷新Vue组件，确保头像等信息立即更新
								this.$forceUpdate()
							}
						} catch (err) {
							console.error('更新Pinia用户信息失败:', err)
						}
					})
					.catch(err => {
						this.$message.error(err.data?.errorMessage || '更新个人信息失败')
					})
			})
		},

		/**
		 * 打开修改密码弹窗
		 */
		openChangePassword() {
			this.changePasswordForm = {
				oldPassword: '',
				newPassword: '',
				confirmPassword: ''
			}
			this.changePasswordDialog.visible = true
		},

		/**
		 * 验证确认密码
		 */
		validateConfirmPassword(rule, value, callback) {
			if (value !== this.changePasswordForm.newPassword) {
				callback(new Error('两次输入的密码不一致'))
			} else {
				callback()
			}
		},

		/**
		 * 保存新密码
		 */
		saveNewPassword() {
			this.$refs.changePasswordFormRef.validate(valid => {
				if (!valid) return

				const params = {
					oldPassword: this.changePasswordForm.oldPassword,
					newPassword: this.changePasswordForm.newPassword
				}

				updateCurrentUserPassword(params)
					.then(() => {
						this.$message.success('密码修改成功，请重新登录')
						this.changePasswordDialog.visible = false
						// 修改密码成功后退出登录
						setTimeout(() => {
							this.onLoginOut()
						}, 1500)
					})
					.catch(err => {
						this.$message.error(err.data?.errorMessage || '修改密码失败')
					})
			})
		},

		/**
		 * 退出登录
		 * 清除本地存储并跳转到登录页
		 */
		onLoginOut() {
			authLoginOut()
				.then(() => {
					// 清空全局配置数据
					clearAllData();
					// 清空 Pinia 存储
					window.$local?.removeAll();
					this.$router.push("/login");
				})
				.catch(err => {
					const errorMessage = err.data?.errorMessage || '退出登录失败';
					this.$message.error(errorMessage);
				});
		},

		/**
		 * 初始化组件
		 * 设置路由列表、当前路径和设备类型
		 */
		init() {
			// 从 Pinia 获取构建好的菜单树
			this.routerList = window.$local?.get('treePermissions') || this.$store?.user?.treePermissions || [];
			console.log('获取到的菜单树:', this.routerList);

			// 获取当前路由路径
			this.active = this.$route.path;

			// 检测设备类型
			this.checkDeviceType();

			// 添加窗口大小变化监听
			this.resizeListener = this.checkDeviceType.bind(this);
			window.addEventListener('resize', this.resizeListener);

			// 初始化小区数据
			this.initCommunityData();
		},

		/**
		 * 初始化小区数据
		 */
		async initCommunityData() {
			try {
				// 从 Pinia 中获取已有的数据
				const storedCommunity = getSelectedCommunity();
				const storedList = window.$local?.get('communityList') || [];

				if (storedList && storedList.length > 0) {
					this.communityList = storedList;
					this.selectedCommunityId = storedCommunity?.id || null;
				} else {
					// 加载小区列表
					await this.loadCommunityList();
				}
			} catch (error) {
				console.error('初始化小区数据失败:', error);
			}
		},

		/**
		 * 加载小区列表
		 */
		async loadCommunityList() {
			this.communityLoading = true;
			try {
				const response = await listCommunity({ pageNum: 1, pageSize: 500 });
				const list = response.data?.data?.list || [];

				this.communityList = list;
				setCommunityList(list);

				// 设置默认选中的小区
				if (list.length > 0) {
					const currentSelected = getSelectedCommunity();
					if (!currentSelected) {
						this.selectedCommunityId = list[0].id;
						setSelectedCommunity(list[0]);
					} else {
						this.selectedCommunityId = currentSelected.id;
					}
				}
			} catch (error) {
				console.error('加载小区列表失败:', error);
				this.$message.error('加载小区列表失败，请稍后重试');
			} finally {
				this.communityLoading = false;
			}
		},

		/**
		 * 小区选择变化处理
		 */
		onCommunityChange(communityId) {
			if (communityId) {
				const selectedCommunity = this.communityList.find(item => item.id === communityId);
				if (selectedCommunity) {
					setSelectedCommunity(selectedCommunity);
					this.$message.success(`已切换到：${selectedCommunity.communityName}`);
				}
			} else {
				setSelectedCommunity(null);
			}
		},

		/**
		 * 选择菜单项（PC端）
		 * @param {string} index - 菜单项索引
		 */
		selectMenu(index) {
			this.active = index;
		},



		/**
		 * 选择菜单项（移动端）
		 * @param {string} index - 菜单项索引
		 */
		handleMobileMenuSelect(index) {
			this.active = index;
			// 选择菜单项后关闭抽屉
			this.closeDrawer();
		},

		/**
		 * 关闭抽屉菜单
		 */
		closeDrawer() {
			this.drawerVisible = false;
		},

		/**
		 * 检测设备类型
		 * 根据窗口宽度判断是否为移动设备
		 */
		checkDeviceType() {
			const wasMobile = this.isMobile;
			this.isMobile = window.innerWidth < BREAKPOINTS.MOBILE;

			// 如果从移动设备切换到PC设备，确保抽屉菜单关闭
			if (wasMobile && !this.isMobile) {
				this.closeDrawer();
			}
		},

		/**
		 * 获取图标组件
		 * 根据图标名称返回对应的图标组件
		 */
		getIconComponent(iconName) {
			return getIconComponent(iconName)
		}
	},

	created() {
		this.init();
	},

	beforeUnmount() {
		// 移除窗口大小变化监听
		if (this.resizeListener) {
			window.removeEventListener('resize', this.resizeListener);
			this.resizeListener = null;
		}
	},

	setup() {
		return {
			Menu
		};
	}
}
</script>

<style>
/**
 * 主布局样式
 *
 * 包含以下部分：
 * 1. CSS变量
 * 2. 主容器
 * 3. 头部导航栏
 * 4. 侧边栏菜单
 * 5. 主内容区域
 * 6. 暗色主题样式
 * 7. 移动端适配
 * 8. 动画效果
 */

/* 1. CSS变量 */
:root {
  --header-height: 60px;
  --sidebar-width: 200px;
  --content-padding: 20px;
  --mobile-content-padding: 10px;
  --mobile-breakpoint: 768px;

  /* 菜单颜色 */
  --menu-bg-color: #314157;
  --menu-text-color: #fff;
  --menu-active-bg-color: rgba(255, 255, 255, 0.2);
  --menu-hover-bg-color: rgba(255, 255, 255, 0.1);

  /* 暗色主题菜单颜色 */
  --dark-menu-bg-color: #1e2a3b;
  --dark-menu-text-color: #e0e0e0;
  --dark-menu-active-bg-color: rgba(64, 158, 255, 0.1);
  --dark-menu-hover-bg-color: #2c3e50;
}

/* 2. 主容器 */
.main-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--background-color);
  color: var(--text-primary);
  transition: all var(--transition-duration);
}

/* 3. 头部导航栏 */
.app-header {
  background-color: #fff;
  box-shadow: var(--box-shadow);
  color: var(--text-primary);
  height: var(--header-height);
  line-height: var(--header-height);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  transition: all var(--transition-duration);
  z-index: 10;
}

/* 头部左侧区域 */
.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 菜单切换按钮 */
.menu-toggle-btn {
  display: none; /* 默认隐藏菜单按钮，只在移动端显示 */
  font-size: 20px;
  padding: 5px;
  border: none;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.menu-toggle-btn:hover {
  transform: scale(1.1);
}

/* 头部标题 */
.header-title h2 {
  margin: 0;
  color: var(--primary-color);
  font-size: 20px;
  font-weight: 600;
  transition: all var(--transition-duration);
}

/* 头部右侧区域 */
.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 小区选择器 */
.community-selector {
  display: flex;
  align-items: center;
}

.community-select {
  width: 200px;
  min-width: 150px;
}

.community-select .el-input__inner {
  font-size: 14px;
}

.empty-text {
  padding: 10px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

/* 欢迎文本 */
.welcome-text {
  display: flex;
  align-items: flex-end;
}

.welcome-label {
  font-size: 16px;
  font-weight: 500;
}

.username {
  font-size: 16px;
  font-weight: 500;
}

.user-role {
  color: #909399;
  font-size: 13px;
}

/* 用户头像 */
.user-avatar {
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
}

/* 退出登录按钮 */
.logout-button {
  color: #F56C6C;
}

/* 内容包装器 */
.content-wrapper {
  flex: 1;
  display: flex;
  overflow: hidden;
  height: calc(100vh - var(--header-height));
}

/* 4. 侧边栏菜单 */
.sidebar {
  background-color: var(--menu-bg-color);
  width: var(--sidebar-width);
  height: 100%;
  transition: all var(--transition-duration);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 5;
}

.sidebar-menu {
  background-color: var(--menu-bg-color);
  border: 0;
  transition: all var(--transition-duration);
  height: 100%;
}

/* 菜单图标样式 */
.menu-icon {
  margin-right: 8px;
  font-size: 16px;
  width: 16px;
  height: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.menu-icon-placeholder {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  display: inline-block;
}

.submenu-icon {
  margin-right: 6px;
  font-size: 14px;
  width: 14px;
  height: 14px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.submenu-icon-placeholder {
  width: 14px;
  height: 14px;
  margin-right: 6px;
  display: inline-block;
}

/* 菜单项样式 */
.sidebar .el-menu-item,
.sidebar .el-sub-menu__title {
  color: var(--menu-text-color) !important;
  height: 50px;
  line-height: 50px;
}

.sidebar .el-menu-item:hover,
.sidebar .el-sub-menu__title:hover {
  background-color: var(--menu-hover-bg-color) !important;
}

.sidebar .el-menu-item.is-active {
  color: #fff !important;
  background-color: var(--menu-active-bg-color) !important;
  font-weight: bold;
}

/* 菜单变量设置 */
.el-menu {
  --el-menu-text-color: var(--menu-text-color);
  --el-menu-active-color: var(--primary-color);
  --el-menu-hover-bg-color: var(--menu-hover-bg-color);
  --el-menu-bg-color: var(--menu-bg-color);
}

/* 修复滚动条样式 */
.el-scrollbar__wrap {
  overflow-x: hidden !important;
}

/* 5. 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow-y: auto;
}

/* 内容容器 */
.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: var(--content-padding);
  min-height: 100%;
  height: 100%;
  border-radius: var(--border-radius);
  transition: all var(--transition-duration);
}

/* 路由视图容器 */
.router-view-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
}

/* 6. 暗色主题样式 */
/* 主容器 */
.dark-theme .main-container {
  background-color: var(--background-color) !important;
}

/* 头部 */
.dark-theme .app-header {
  background-color: #1e1e1e !important;
  color: var(--text-primary) !important;
  border-bottom: 1px solid #333 !important;
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.3) !important;
}

.dark-theme .header-title h2 {
  color: #409EFF !important;
}

/* 侧边栏 */
.dark-theme .sidebar,
.dark-theme .sidebar-menu {
  background-color: var(--dark-menu-bg-color) !important;
  border-right: 1px solid #333 !important;
}

.dark-theme .el-menu {
  --el-menu-bg-color: var(--dark-menu-bg-color) !important;
  --el-menu-text-color: var(--dark-menu-text-color) !important;
  --el-menu-active-color: #409EFF !important;
  --el-menu-hover-bg-color: var(--dark-menu-hover-bg-color) !important;
  --el-menu-border-color: #333 !important;
}

/* 菜单项 */
.dark-theme .sidebar .el-menu-item,
.dark-theme .sidebar .el-sub-menu__title,
.dark-theme .el-menu-item,
.dark-theme .el-sub-menu__title {
  color: var(--dark-menu-text-color) !important;
  background-color: var(--dark-menu-bg-color) !important;
}

.dark-theme .sidebar .el-menu-item:hover,
.dark-theme .sidebar .el-sub-menu__title:hover,
.dark-theme .el-menu-item:hover,
.dark-theme .el-sub-menu__title:hover {
  background-color: var(--dark-menu-hover-bg-color) !important;
}

.dark-theme .sidebar .el-menu-item.is-active,
.dark-theme .el-menu-item.is-active {
  color: #409EFF !important;
  background-color: var(--dark-menu-active-bg-color) !important;
}

/* 子菜单 */
.dark-theme .el-menu--popup,
.dark-theme .el-menu--popup-container,
.dark-theme .el-menu--popup .el-menu-item,
.dark-theme .el-menu--popup .el-sub-menu__title {
  background-color: var(--dark-menu-bg-color) !important;
  color: var(--dark-menu-text-color) !important;
}

/* 滚动条区域 */
.dark-theme .el-aside,
.dark-theme .el-scrollbar,
.dark-theme .el-scrollbar__view,
.dark-theme .el-scrollbar__wrap {
  background-color: var(--dark-menu-bg-color) !important;
}

.dark-theme .el-scrollbar__bar {
  background-color: transparent !important;
}

/* 主内容区 */
.dark-theme .main-content,
.dark-theme .content-container {
  background-color: var(--background-color) !important;
  color: var(--text-primary) !important;
}

/* 确保内容区域中的所有元素都应用暗色主题 */
.dark-theme .el-main > div,
.dark-theme .el-main > section,
.dark-theme .el-main > article {
  color: var(--text-primary) !important;
}

/* 7. 移动端适配样式 */
@media (max-width: 768px) {
  /* 显示菜单按钮 */
  .menu-toggle-btn {
    display: block;
  }

  /* 隐藏PC端侧边栏 */
  .desktop-sidebar {
    display: none;
  }

  /* 调整头部样式 */
  .app-header {
    padding: 0 var(--mobile-content-padding);
  }

  /* 隐藏欢迎文本 */
  .welcome-text {
    display: none;
  }

  /* 移动端小区选择器调整 */
  .community-selector {
    order: -1; /* 将小区选择器移到最前面 */
  }

  .community-select {
    width: 150px;
    min-width: 120px;
  }

  /* 调整头部标题 */
  .header-title h2 {
    font-size: 18px;
  }

  /* 调整主内容区域 */
  .main-content {
    padding: var(--mobile-content-padding);
  }
}

/* 抽屉菜单样式 */
.mobile-drawer .el-drawer__body {
  padding: 0;
  background-color: var(--menu-bg-color);
}

.dark-theme .mobile-drawer .el-drawer__body {
  background-color: var(--dark-menu-bg-color) !important;
}

/* 抽屉菜单关闭按钮 */
.mobile-drawer .el-drawer__close-btn {
  color: var(--menu-text-color);
}

.dark-theme .mobile-drawer .el-drawer__close-btn {
  color: var(--dark-menu-text-color);
}

/* 8. 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(-20px);
  opacity: 0;
}

/* 头像上传样式 */
.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  width: 120px;
  height: 120px;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-uploader .el-upload {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 28px;
  height: 28px;
}

.avatar {
  width: 120px;
  height: 120px;
  object-fit: cover;
  border-radius: 6px;
  display: block;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
  text-align: center;
}
</style>