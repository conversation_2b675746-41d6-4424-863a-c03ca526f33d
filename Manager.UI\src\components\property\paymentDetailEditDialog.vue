<template>
  <el-dialog 
    :title="dialog.title" 
    v-model="dialog.show" 
    width="800px" 
    top="5vh" 
    :close-on-click-modal="false" 
    class="payment-detail-edit-dialog"
    append-to-body
  >
    <el-form :model="paymentDetailModel" :rules="rules" ref="formRef" label-width="120px" class="payment-detail-edit-form">
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="缴费对象">
            <el-input v-model="objectDisplay" readonly />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="缴费项目" required>
            <div class="item-selector">
              <el-input v-model="itemDisplay" placeholder="请选择缴费项目" readonly style="width: calc(100% - 100px);" />
              <el-button type="primary" @click="openItemSelector" style="margin-left: 10px;">选择项目</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="单价" prop="unitPrice">
            <el-input-number v-model="paymentDetailModel.unitPrice" :min="0" :precision="2" 
              style="width: 100%;" placeholder="请输入单价" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单位" prop="unit">
            <el-input v-model="paymentDetailModel.unit" maxlength="20" placeholder="如：度、平方米" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="数量" prop="quantity">
            <el-input-number v-model="paymentDetailModel.quantity" :min="0" :precision="2" 
              style="width: 100%;" placeholder="请输入数量" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="折扣" prop="discount">
            <el-input-number v-model="paymentDetailModel.discount" :min="0" :max="10" :precision="1" 
              style="width: 100%;" placeholder="0-10折" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态" prop="status">
            <el-select v-model="paymentDetailModel.status" placeholder="请选择状态" style="width: 100%;">
              <el-option 
                v-for="item in statusOptions" 
                :key="item.value" 
                :label="item.label" 
                :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="计费周期" prop="billingCycle">
            <el-select v-model="paymentDetailModel.billingCycle" placeholder="请选择计费周期" style="width: 100%;">
              <el-option label="月度" value="monthly" />
              <el-option label="季度" value="quarterly" />
              <el-option label="年度" value="yearly" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="账单日" prop="billDay">
            <el-input v-model="paymentDetailModel.billDay" maxlength="50" 
              placeholder="格式：0_6_18:00（月度:每月6号18:00）" clearable />
            <div class="help-text">
              <small>格式说明：月度:每月6号18:00 → 0_6_18:00；季度:每季度开始第一个月的16号18:00 → 1_16_18:00；年度:每年7月30号18:00 → 7_30_18:00</small>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="生效日期" prop="effectiveDate">
            <el-date-picker v-model="paymentDetailModel.effectiveDate" type="date" 
              placeholder="请选择生效日期" style="width: 100%;" value-format="YYYY-MM-DD" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="失效日期" prop="expiringDate">
            <el-date-picker v-model="paymentDetailModel.expiringDate" type="date" 
              placeholder="请选择失效日期" style="width: 100%;" value-format="YYYY-MM-DD" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="备注" prop="note">
            <el-input v-model="paymentDetailModel.note" type="textarea" :rows="3" 
              maxlength="300" placeholder="请输入备注" show-word-limit />
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 金额预览 -->
      <el-row :gutter="16" v-if="paymentDetailModel.unitPrice && paymentDetailModel.quantity">
        <el-col :span="24">
          <el-form-item label="金额预览">
            <div class="amount-preview">
              <span>单价：¥{{ paymentDetailModel.unitPrice }} × 数量：{{ paymentDetailModel.quantity }} × 折扣：{{ paymentDetailModel.discount || 10 }}折 = </span>
              <span style="color: #f56c6c; font-weight: bold; font-size: 16px;">¥{{ calculateAmount() }}</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <!-- 缴费项目选择弹窗 -->
    <el-dialog title="选择缴费项目" v-model="itemSelectorDialog" width="800px" append-to-body>
      <div class="item-search">
        <el-input v-model="itemSearchKeyword" placeholder="搜索项目名称" clearable style="width: 300px; margin-bottom: 16px;" @input="searchItems" />
      </div>
      <el-table :data="itemList" @row-click="selectItem" style="cursor: pointer;" max-height="400">
        <el-table-column prop="paymentItemName" label="项目名称" width="200" align="center"/>
        <el-table-column prop="paymentItemDescribe" label="项目描述" align="center" show-overflow-tooltip/>
        <el-table-column prop="unitPrice" label="单价" width="100" align="center">
          <template #default="scope">
            <span style="color: #f56c6c; font-weight: bold;">¥{{ scope.row.unitPrice }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" align="center"/>
        <el-table-column prop="billingCycle" label="计费周期" width="100" align="center">
          <template #default="scope">
            <span>{{ getBillingCycleLabel(scope.row.billingCycle) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="isActive" label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'danger'" size="small">
              {{ scope.row.isActive ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-col" style="margin-top: 16px;">
        <el-pagination background layout="prev, pager, next" @current-change="itemPageChange" 
          :total="itemTotal" :page-size="itemSearchModel.pageSize" :current-page="itemSearchModel.pageNum" />
      </div>
    </el-dialog>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialog.show = false">取消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { addPropertyPaymentDetail, editPropertyPaymentDetail, listPropertyPaymentItems } from '@/api/property/paymentItems'
import { listDictByNameEn } from '@/api/system/dict'
import mitt from '@/utils/mitt'

export default {
  name: 'PaymentDetailEditDialog',
  props: {
    paymentObject: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      loading: false,
      paymentDetailModel: {
        id: undefined,
        paymentItemSnapshot: '',
        unitPrice: null,
        unit: '',
        quantity: null,
        discount: 10,
        status: 'active',
        note: '',
        communityId: null,
        paymentObjectId: null,
        billingCycle: '',
        billDay: '',
        effectiveDate: '',
        expiringDate: ''
      },
      dialog: {
        show: false,
        title: ''
      },
      objectDisplay: '', // 缴费对象显示信息
      itemDisplay: '', // 缴费项目显示信息
      selectedPaymentItem: null, // 选中的缴费项目
      itemSelectorDialog: false,
      itemSearchKeyword: '',
      itemList: [],
      itemTotal: 0,
      itemSearchModel: {
        pageNum: 1,
        pageSize: 10
      },
      statusOptions: [],
      rules: {
        unitPrice: [
          { required: true, message: '请输入单价', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '请输入单位', trigger: 'blur' }
        ],
        quantity: [
          { required: true, message: '请输入数量', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        billingCycle: [
          { required: true, message: '请选择计费周期', trigger: 'change' }
        ]
      }
    }
  },
  
  methods: {
    /**
     * 计算金额
     */
    calculateAmount() {
      const amount = (this.paymentDetailModel.unitPrice || 0) * (this.paymentDetailModel.quantity || 0) * (this.paymentDetailModel.discount || 10) / 10
      return amount.toFixed(2)
    },
    
    /**
     * 获取计费周期标签
     */
    getBillingCycleLabel(cycle) {
      const cycleMap = {
        'monthly': '月度',
        'quarterly': '季度',
        'yearly': '年度'
      }
      return cycleMap[cycle] || cycle || '--'
    },
    
    /**
     * 打开缴费项目选择器
     */
    openItemSelector() {
      this.itemSelectorDialog = true
      this.searchItems()
    },
    
    /**
     * 搜索缴费项目
     */
    searchItems() {
      const params = {
        ...this.itemSearchModel,
        communityId: this.paymentObject.communityId
      }
      
      if (this.itemSearchKeyword) {
        params.paymentItemName = this.itemSearchKeyword
      }
      
      listPropertyPaymentItems(params).then(res => {
        this.itemList = res.data.data.list || []
        this.itemTotal = res.data.data.total || 0
      }).catch(err => {
        console.error('搜索缴费项目失败:', err)
      })
    },
    
    /**
     * 选择缴费项目
     */
    selectItem(item) {
      this.selectedPaymentItem = item
      this.itemDisplay = `${item.paymentItemName} (¥${item.unitPrice}/${item.unit})`
      
      // 自动填充项目信息
      this.paymentDetailModel.unitPrice = item.unitPrice
      this.paymentDetailModel.unit = item.unit
      this.paymentDetailModel.billingCycle = item.billingCycle
      
      // 保存项目快照为JSON字符串
      this.paymentDetailModel.paymentItemSnapshot = JSON.stringify({
        id: item.id,
        paymentItemName: item.paymentItemName,
        paymentItemDescribe: item.paymentItemDescribe,
        unitPrice: item.unitPrice,
        unit: item.unit,
        billingCycle: item.billingCycle,
        isActive: item.isActive,
        note: item.note
      })
      
      this.itemSelectorDialog = false
    },
    
    /**
     * 缴费项目分页变化
     */
    itemPageChange(num) {
      this.itemSearchModel.pageNum = num
      this.searchItems()
    },
    
    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        // 加载缴费状态字典
        const statusRes = await listDictByNameEn('payment_status')
        this.statusOptions = (statusRes.data.data || []).map(item => ({
          label: item.nameCn,
          value: item.nameEn
        }))
        
        // 如果字典为空，使用默认选项
        if (this.statusOptions.length === 0) {
          this.statusOptions = [
            { value: 'active', label: '有效' },
            { value: 'inactive', label: '无效' },
            { value: 'pending', label: '待处理' },
            { value: 'expired', label: '已过期' }
          ]
        }
      } catch (err) {
        console.error('加载字典数据失败:', err)
        // 使用默认状态选项
        this.statusOptions = [
          { value: 'active', label: '有效' },
          { value: 'inactive', label: '无效' },
          { value: 'pending', label: '待处理' },
          { value: 'expired', label: '已过期' }
        ]
      }
    },
    
    /**
     * 重置表单
     */
    resetForm() {
      this.$refs.formRef && this.$refs.formRef.resetFields()
      this.paymentDetailModel = {
        id: undefined,
        paymentItemSnapshot: '',
        unitPrice: null,
        unit: '',
        quantity: null,
        discount: 10,
        status: 'active',
        note: '',
        communityId: this.paymentObject.communityId || null,
        paymentObjectId: this.paymentObject.id || null,
        billingCycle: '',
        billDay: '',
        effectiveDate: '',
        expiringDate: ''
      }
      this.selectedPaymentItem = null
      this.itemDisplay = ''
      this.itemSearchKeyword = ''
    },
    
    /**
     * 提交表单
     */
    submit() {
      // 手动验证缴费项目选择
      if (!this.selectedPaymentItem) {
        this.$message.warning('请选择缴费项目')
        return
      }

      this.$refs.formRef.validate(valid => {
        if (!valid) return
        
        this.loading = true
        const api = this.paymentDetailModel.id ? editPropertyPaymentDetail : addPropertyPaymentDetail
        
        api(this.paymentDetailModel).then(() => {
          this.$message.success(this.paymentDetailModel.id ? '修改成功' : '添加成功')
          this.dialog.show = false
          this.$emit('search')
        }).catch(err => {
          this.$message.error(err.data?.errorMessage || '操作失败')
        }).finally(() => {
          this.loading = false
        })
      })
    }
  },
  
  async mounted() {
    await this.initDictData()
    
    mitt.on('openPaymentDetailEditDialogAdd', (paymentObject) => {
      this.resetForm()
      this.objectDisplay = `${paymentObject.moniker} (${paymentObject.phone || paymentObject.email || 'ID:' + paymentObject.id})`
      this.dialog.title = '新增缴费明细'
      this.dialog.show = true
    })
    
    mitt.on('openPaymentDetailEditDialogEdit', (data) => {
      this.resetForm()
      this.paymentDetailModel = { ...data }
      this.objectDisplay = `${data.paymentObject.moniker} (${data.paymentObject.phone || data.paymentObject.email || 'ID:' + data.paymentObject.id})`
      
      // 解析项目快照显示
      if (data.paymentItemSnapshot) {
        try {
          const item = JSON.parse(data.paymentItemSnapshot)
          this.selectedPaymentItem = item
          this.itemDisplay = `${item.paymentItemName} (¥${item.unitPrice}/${item.unit})`
        } catch (error) {
          this.itemDisplay = data.paymentItemSnapshot
        }
      }
      
      this.dialog.title = '编辑缴费明细'
      this.dialog.show = true
    })
  },
  
  beforeUnmount() {
    mitt.off('openPaymentDetailEditDialogAdd')
    mitt.off('openPaymentDetailEditDialogEdit')
  }
}
</script>

<style scoped>
.payment-detail-edit-dialog {
  border-radius: 8px;
}

.payment-detail-edit-form {
  padding: 0 16px;
}

.item-selector {
  display: flex;
  align-items: center;
}

.item-search {
  margin-bottom: 16px;
}

.amount-preview {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.help-text {
  margin-top: 4px;
  color: #909399;
}

.pagination-col {
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 深色主题适配 */
.dark-theme .payment-detail-edit-dialog {
  background-color: var(--card-background);
}

.dark-theme .amount-preview {
  background-color: var(--card-background);
  border-color: #444;
}
</style>
