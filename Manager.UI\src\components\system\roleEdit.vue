<template>
  <el-dialog
    width="35%"
    v-loading="loading"
    destroy-on-close
    v-model="dialog.show"
    :title="dialog.title"
  >
    <el-form :rules="rules" ref="form" :model="roleModel" label-width="100px">
      <el-row>
        <el-col>
          <el-form-item label="角色名" prop="roleName">
            <el-input
              v-model="roleModel.roleName"
              placeholder="请输入角色名"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="角色码" prop="roleCode">
            <el-input
              v-model="roleModel.roleCode"
              placeholder="请输入角色码"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="所属组织" prop="orgId">
            <el-cascader
              v-model="roleModel.orgId"
              :options="orgTreeData"
              :props="orgProps"
              placeholder="请选择组织"
              style="width: 100%"
              clearable
              filterable
              :show-all-levels="false"
            >
            </el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="数据权限" prop="dataScope">
            <el-select
              v-model="roleModel.dataScope"
              placeholder="请选择数据权限"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item in dataScopeOptions"
                :key="item.nameEn"
                :label="item.nameCn"
                :value="item.nameEn"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="排序" prop="sort">
            <el-input
              v-model="roleModel.sort"
              placeholder="请输入排序"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              :rows="2"
              v-model="roleModel.remark"
              placeholder="请输入备注内容"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="权限关联">
            <el-radio-group v-model="isStrictly">
              <el-radio :label="true">关闭关联</el-radio>
              <el-radio :label="false">开启关联</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <el-form-item label="权限" prop="permissions">
            <el-tree
              :check-strictly="isStrictly"
              @check="menuCheck"
              :default-checked-keys="roleModel.permissions"
              :data="processedAuthorityList"
              show-checkbox
              node-key="id"
              :props="permissionProps"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row justify="center">
      <el-button
        type="primary"
        style="width: 100px; height: 30px; margin-top: 20px"
        @click="onSubmit"
        >提交</el-button
      >
    </el-row>
  </el-dialog>
</template>

<script>
import { addRole, editRole } from "@/api/system/role";
import { getOrgTree } from "@/api/system/org";
import { listDictByNameEn } from "@/api/system/dict";
import mitt from "@/utils/mitt";
export default {
  props: ["authorityList"],
  data() {
    return {
      loading: false,
      isStrictly: true,
      roleModel: {
        orgId: null,
        dataScope: "",
      },
      dialog: {},
      orgTreeData: [],
      dataScopeOptions: [],
      orgProps: {
        value: "id",
        label: "orgName",
        children: "children",
        emitPath: false,
        checkStrictly: true,
        expandTrigger: "click",
      },
      permissionProps: {
        children: "children",
        label: "menuName",
      },
      rules: {
        roleName: [
          {
            required: true,
            message: "请输入角色名",
            trigger: "blur",
          },
        ],
        roleCode: [
          {
            required: true,
            message: "请输入角色码",
            trigger: "blur",
          },
        ],
      },
    };
  },
  computed: {
    /**
     * 处理权限数据，为非menu和非permis类型的节点添加disabled属性
     */
    processedAuthorityList() {
      const processNode = (node) => {
        const processed = { ...node };

        // 如果menuType不是'menu'或'permis'，则禁用复选框
        if (processed.menuType !== 'menu' && processed.menuType !== 'permis') {
          processed.disabled = true;
        }

        // 递归处理子节点
        if (processed.children && processed.children.length > 0) {
          processed.children = processed.children.map(child => processNode(child));
        }

        return processed;
      };

      return this.authorityList.map(node => processNode(node));
    }
  },
  methods: {
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 准备提交数据，过滤掉permissions中的null值
          const submitData = { ...this.roleModel };
          if (submitData.permissions && Array.isArray(submitData.permissions)) {
            submitData.permissions = submitData.permissions.filter(permission => permission !== null && permission !== undefined);
          }

          if (this.roleModel.id == 0) {
            addRole(submitData)
              .then(() => {
                this.$message.success("操作成功");
                this.$emit("search");
                this.dialog.show = false;
              })
              .catch((err) => {
                this.$message.error(err.data.errorMessage);
              });
          } else {
            editRole(submitData)
              .then(() => {
                this.$message.success("操作成功");
                this.$emit("search");
                this.dialog.show = false;
              })
              .catch((err) => {
                this.$message.error(err.data.errorMessage);
              });
          }
        }
      });
    },
    menuCheck(node, checked) {
      this.roleModel.permissions = checked.checkedKeys;
    },
    /**
     * 加载组织树数据
     */
    loadOrgTree() {
       
      getOrgTree()
        .then((res) => {
           
          this.orgTreeData = res.data.data.list || [];
        })
        .catch((err) => {
          console.error("加载组织树失败:", err);
          this.$message.error("加载组织数据失败");
          this.orgTreeData = [];
        });
    },

    /**
     * 加载数据权限选项
     */
    loadDataScopeOptions() {
      listDictByNameEn("data_scope")
        .then((res) => {
           
          this.dataScopeOptions = res.data.data || [];
        })
        .catch((err) => {
          console.error("加载数据权限选项失败:", err);
          this.$message.error("加载数据权限选项失败");
          this.dataScopeOptions = [];
        });
    },
  },
  mounted() {
    // 加载基础数据
    this.loadOrgTree();
    this.loadDataScopeOptions();

    this.$nextTick(function () {
      mitt.on("openRoleEdit", (role) => {
        this.roleModel = {
          ...role,
          orgId: role.orgId || null,
          dataScope: role.dataScope || "",
        };
        this.dialog.show = true;
        this.dialog.title = "修改信息";
      });
      mitt.on("openRoleAdd", () => {
        this.roleModel = {
          id: 0,
          orgId: null,
          dataScope: "",
        };
        this.dialog.show = true;
        this.dialog.title = "添加角色";
      });
    });
  },
};
</script>

<style scoped>
.role-edit-form :deep(.el-form-item) {
  margin-bottom: 12px !important;
}
</style> 