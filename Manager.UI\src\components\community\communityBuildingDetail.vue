<template>
  <el-dialog
    :title="dialog.title"
    v-model="dialog.show"
    width="1200px"
    class="building-detail-dialog"
  >
    <div class="building-detail-content">
      <!-- 楼栋信息 -->
      <div class="building-info">
        <h3>{{ buildingInfo.buildingNumber }}</h3>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">楼栋ID：</span>
            <span class="value">{{ buildingInfo.id }}</span>
          </div>
          <div class="info-item">
            <span class="label">序号：</span>
            <span class="value">{{ buildingInfo.sort || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{ buildingInfo.createTime || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">更新时间：</span>
            <span class="value">{{ buildingInfo.updateTime || "-" }}</span>
          </div>
          <div class="info-item full-width">
            <span class="label">备注：</span>
            <span class="value">{{ buildingInfo.note || "无" }}</span>
          </div>
        </div>
      </div>

      <!-- 房间列表 -->
      <div class="rooms-section">
        <div class="section-header">
          <h4>房间列表 ({{ roomList.length }}间)</h4>
          <el-button type="primary" size="small" @click="addRoom"
            >添加房间</el-button
          >
        </div>

        <!-- 搜索区域 -->
        <div class="search-area">
          <el-input
            v-model="roomSearch.roomNumber"
            placeholder="房间号"
            clearable
            style="width: 200px; margin-right: 16px"
          />
          <el-input
            v-model="roomSearch.unitNumber"
            placeholder="单元号"
            clearable
            style="width: 200px; margin-right: 16px"
          />
          <el-button type="primary" @click="searchRooms">搜索</el-button>
        </div>

        <!-- 房间表格 -->
        <div class="rooms-table">
          <el-table :data="roomList" style="width: 100%" max-height="400">
            <el-table-column
              prop="roomNumber"
              label="房间号"
              width="120"
              align="center"
            />
            <el-table-column
              prop="unitNumber"
              label="单元号"
              width="120"
              align="center"
            />
            <el-table-column
              prop="type"
              label="户型"
              width="120"
              align="center"
            >
              <template #default="scope">
                {{ getRoomTypeLabel(scope.row.type) }}
              </template>
            </el-table-column>

            <el-table-column
              prop="area"
              label="面积(㎡)"
              width="120"
              align="center"
            >
              <template #default="scope">
                <span v-if="scope.row.area">{{ scope.row.area }}㎡</span>
                <span v-else>-</span>
              </template>
            </el-table-column>

            <el-table-column
              prop="createTime"
              label="创建时间"
              align="center"
            />
            <el-table-column
              prop="note"
              label="备注"
              align="center"
              show-overflow-tooltip
            />
            <el-table-column label="操作" width="150" align="center">
              <template #default="scope">
                <el-button type="text" size="mini" @click="editRoom(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  type="text"
                  size="mini"
                  @click="deleteRoom(scope.row.id)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-area" v-if="roomTotal > 0">
          <el-pagination
            background
            layout="prev, pager, next"
            @current-change="roomCurrentChange"
            :total="roomTotal"
            :page-size="roomSearchModel.pageSize"
            :current-page="roomSearchModel.pageNum"
          />
        </div>
      </div>
    </div>

    <!-- 房间编辑弹窗 -->
    <community-room-edit @search="loadRooms" ref="roomEditDialog"  :roomTypeList="roomTypeList"/>
  </el-dialog>
</template>

<script>
import {
  listCommunityRoom,
  deleteCommunityRoom,
} from "@/api/community/communityRoom";
import communityRoomEdit from "@/components/community/communityRoomEdit.vue";
import mitt from "@/utils/mitt";
import { listDictByNameEn } from '@/api/system/dict'
export default {
  name: "CommunityBuildingDetail",
  components: {
    communityRoomEdit,
  },
  data() {
    return {
      dialog: {
        show: false,
        title: "楼栋详情",
      },
      buildingInfo: {},
      roomList: [],
      roomTotal: 0,
      roomSearchModel: {
        pageNum: 1,
        pageSize: 10,
        buildingId: null,
        roomNumber: "",
        unitNumber: "",
      },
      roomSearch: {
        roomNumber: "",
        unitNumber: "",
      },
      roomTypeList: [],
    };
  },
  methods: {
    loadRooms() {
      if (!this.buildingInfo.id) return;

      this.roomSearchModel.buildingId = this.buildingInfo.id;
      listCommunityRoom(this.roomSearchModel)
        .then((res) => {
          this.roomList = res.data.data.list || [];
          this.roomTotal = res.data.data.total || 0;
        })
        .catch((err) => {
          console.error("加载房间列表失败:", err);
          this.roomList = [];
          this.roomTotal = 0;
        });
    },
    searchRooms() {
      this.roomSearchModel.roomNumber = this.roomSearch.roomNumber;
      this.roomSearchModel.unitNumber = this.roomSearch.unitNumber;
      this.roomSearchModel.pageNum = 1;
      this.loadRooms();
    },

    //获取户型标签
    getRoomTypeLabel(roomType) {
      const item = this.roomTypeList.find((item) => item.nameEn === roomType);
      return item ? item.nameCn : roomType;
    },
    roomCurrentChange(num) {
      this.roomSearchModel.pageNum = num;
      this.loadRooms();
    },
    addRoom() {
      mitt.emit("openRoomAdd", this.buildingInfo.id);
    },
    editRoom(room) {
      mitt.emit("openRoomEdit", room);
    },
    deleteRoom(id) {
      this.$confirm("删除房间, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteCommunityRoom(id).then(() => {
            this.$message.success("删除成功");
            this.loadRooms();
          });
        })
        .catch(() => {});
    },

    
    /**
     * 初始化字典数据
     */
    async initDictData() {
      try {
        debugger
        const roomTypeRes = await listDictByNameEn("room_type");
 
        this.roomTypeList = roomTypeRes.data.data || [];
      } catch (err) {
        console.error("加载户型字典失败:", err);
      
      }
    },

  },
  mounted() {

 // 初始化字典数据
    this.initDictData();

    mitt.on("openCommunityBuildingDetail", (building) => {
      this.buildingInfo = building;
      this.dialog.title = `楼栋详情 - ${building.buildingNumber}`;
      this.dialog.show = true;

      // 重置搜索条件
      this.roomSearch = {
        roomNumber: "",
        unitNumber: "",
      };
      this.roomSearchModel = {
        pageNum: 1,
        pageSize: 10,
        buildingId: building.id,
        roomNumber: "",
        unitNumber: "",
      };

      this.loadRooms();
    });
  },
  beforeDestroy() {
    mitt.off("openCommunityBuildingDetail");
  },
};
</script>

<style scoped>
.building-detail-content {
  padding: 20px;
}

.building-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.building-info h3 {
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.label {
  font-weight: 500;
  margin-right: 8px;
  opacity: 0.9;
}

.value {
  opacity: 0.95;
}

.rooms-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f2f5;
}

.section-header h4 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.search-area {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.rooms-table {
  margin-bottom: 16px;
}

.pagination-area {
  display: flex;
  justify-content: center;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.building-detail-dialog >>> .el-dialog__body {
  padding: 0;
  max-height: 80vh;
  overflow-y: auto;
}

.building-detail-dialog >>> .el-table {
  border-radius: 8px;
  overflow: hidden;
}

.building-detail-dialog >>> .el-table th {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}
</style>
